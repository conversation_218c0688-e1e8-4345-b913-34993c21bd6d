# QwikBanka Core Banking System - Production Configuration
# Optimized for high-performance banking operations

---
grails:
    profile: web
    codegen:
        defaultPackage: org.icbs
    gorm:
        reactor:
            # Whether to translate GORM events into Reactor events
            # Disabled for production performance
            events: false
        default:
            mapping:
                # Enable second-level cache for better performance
                cache: true
                # Optimize batch operations
                'batch-size': 50
                # Enable lazy loading optimization
                'lazy': true
            # Connection pool optimization
            pooled: true
            jmxExport: true
            driverClassName: org.postgresql.Driver
            dialect: org.hibernate.dialect.PostgreSQL10Dialect
            
    # Performance optimizations
    views:
        # Enable view caching in production
        cache: true
        # Compress views for faster delivery
        compress: true
    
    # Asset pipeline optimization
    assets:
        # Enable asset compilation and minification
        compile: true
        minify: true
        # Enable GZIP compression
        enableGzip: true
        # Cache assets for 1 year
        cacheHeaders: 
            'Cache-Control': 'public, max-age=********'

info:
    app:
        name: '@info.app.name@'
        version: '@info.app.version@'
        grailsVersion: '@info.app.grailsVersion@'

spring:
    jmx:
        unique-names: true
    
    # Database connection pool optimization (HikariCP)
    datasource:
        pooled: true
        jmxExport: true
        driverClassName: org.postgresql.Driver
        username: ${DB_USERNAME:qwikbanka_prod}
        password: ${DB_PASSWORD:secure_password}
        url: ${DB_URL:***********************************************}
        
        # HikariCP Performance Settings
        type: com.zaxxer.hikari.HikariDataSource
        hikari:
            # Connection pool sizing
            minimum-idle: 10
            maximum-pool-size: 50
            # Connection timeout settings
            connection-timeout: 30000
            idle-timeout: 600000
            max-lifetime: 1800000
            # Performance optimizations
            leak-detection-threshold: 60000
            validation-timeout: 5000
            initialization-fail-timeout: 1
            # Connection test query
            connection-test-query: SELECT 1
            # Pool name for monitoring
            pool-name: QwikBankaHikariCP
            
        # Additional database optimizations
        properties:
            # PostgreSQL specific optimizations
            tcpKeepAlive: true
            socketTimeout: 30
            loginTimeout: 10
            # Prepared statement cache
            preparedStatementCacheSize: 250
            preparedStatementCacheSqlLimit: 2048
            # SSL settings for production
            ssl: true
            sslmode: require

# Hibernate performance optimizations
hibernate:
    # Enable second-level cache
    cache:
        use_second_level_cache: true
        use_query_cache: true
        region:
            factory_class: org.hibernate.cache.ehcache.EhCacheRegionFactory
    
    # SQL optimization
    show_sql: false
    format_sql: false
    use_sql_comments: false
    
    # Batch processing optimization
    jdbc:
        batch_size: 50
        batch_versioned_data: true
        order_inserts: true
        order_updates: true
    
    # Connection handling
    connection:
        provider_disables_autocommit: true
        
    # Performance monitoring
    generate_statistics: true
    
    # Query optimization
    query:
        plan_cache_max_size: 2048
        substitutions:
            true: 1
            false: 0

# Caching configuration
cache:
    # Enable distributed caching for production
    ehcache:
        ehcacheXmlLocation: 'classpath:ehcache-production.xml'
        replicationAsynchronously: true
        replicationInterval: 1000
    
    # Redis cache configuration (if using Redis)
    redis:
        enabled: ${REDIS_ENABLED:false}
        host: ${REDIS_HOST:localhost}
        port: ${REDIS_PORT:6379}
        password: ${REDIS_PASSWORD:}
        database: ${REDIS_DATABASE:0}
        timeout: 2000ms
        
# Logging configuration for production
logging:
    level:
        ROOT: WARN
        org.icbs: INFO
        org.springframework.security: WARN
        org.hibernate: WARN
        org.hibernate.SQL: WARN
        org.hibernate.type.descriptor.sql.BasicBinder: WARN
        grails.app: INFO
        
    # Log to files in production
    file:
        name: /var/log/qwikbanka/application.log
        max-size: 100MB
        max-history: 30
        
    pattern:
        file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
        console: '%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

# Security configurations
security:
    # Session management
    session:
        timeout: 1800 # 30 minutes
        cookie:
            secure: true
            httpOnly: true
            sameSite: strict
            
    # CSRF protection
    csrf:
        enabled: true
        
    # Content Security Policy
    headers:
        contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
        frameOptions: DENY
        contentTypeOptions: nosniff
        xssProtection: "1; mode=block"

# Banking system specific configurations
banking:
    # Transaction limits
    transaction:
        maxDailyLimit: 1000000
        maxSingleLimit: 500000
        timeoutSeconds: 300
        
    # Interest calculation
    interest:
        calculationPrecision: 6
        roundingMode: HALF_UP
        
    # Audit settings
    audit:
        enabled: true
        retentionDays: 2555 # 7 years
        
    # Performance monitoring
    monitoring:
        enabled: true
        metricsInterval: 60000 # 1 minute
        
# JVM optimization settings (for reference)
# These should be set in the startup script:
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+UseStringDeduplication
# -XX:+OptimizeStringConcat
# -Djava.awt.headless=true
# -Dfile.encoding=UTF-8

# Application monitoring
management:
    endpoints:
        web:
            exposure:
                include: health,info,metrics,prometheus
    endpoint:
        health:
            show-details: when-authorized
        metrics:
            enabled: true
    metrics:
        export:
            prometheus:
                enabled: true
                
# Server configuration
server:
    # Production port
    port: ${SERVER_PORT:8080}
    
    # Connection settings
    tomcat:
        max-threads: 200
        min-spare-threads: 10
        max-connections: 8192
        accept-count: 100
        connection-timeout: 20000
        
    # Compression
    compression:
        enabled: true
        mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
        min-response-size: 1024
        
    # SSL configuration (if using HTTPS)
    ssl:
        enabled: ${SSL_ENABLED:false}
        key-store: ${SSL_KEYSTORE:}
        key-store-password: ${SSL_KEYSTORE_PASSWORD:}
        key-store-type: PKCS12
