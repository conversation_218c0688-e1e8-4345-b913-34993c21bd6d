# QwikBanka Application Configuration
# SECURITY ENHANCED CONFIGURATION

grails:
  profile: web
  codegen:
    defaultPackage: org.icbs
  gorm:
    reactor:
      events: false
    # Enhanced GORM configuration for banking performance
    failOnError: true
    autoFlush: false
    flushMode: COMMIT
    default:
      mapping:
        cache: true
        batchSize: 25
        fetchMode: 'select'
  cache:
    enabled: true
    cleanAtStartup: false
    # Enhanced cache configuration for banking performance
    cacheManager: org.springframework.cache.caffeine.CaffeineCacheManager
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=30m,expireAfterAccess=15m,recordStats
  views:
    gsp:
      encoding: UTF-8
      htmlcodec: xml
      codecs:
        expression: html
        scriptlet: html
        taglib: none
        staticparts: none
      sitemesh:
        preprocess: true
      keepgenerateddir: false
      trimWhitespace: true
  mime:
    disable:
      accept:
        header:
          userAgents:
            - Gecko
            - WebKit
            - Presto
            - Trident
    types:
      all: '*/*'
      atom: application/atom+xml
      css: text/css
      csv: text/csv
      form: application/x-www-form-urlencoded
      html:
        - text/html
        - application/xhtml+xml
      js: text/javascript
      json:
        - application/json
        - text/json
      multipartForm: multipart/form-data
      pdf: application/pdf
      rss: application/rss+xml
      text: text/plain
      hal:
        - application/hal+json
        - application/hal+xml
      xml:
        - text/xml
        - application/xml

# SECURITY CONFIGURATION
spring:
  security:
    csrf:
      enabled: true
  datasource:
    # SECURITY FIX: Use HikariCP for optimal performance and security
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: org.postgresql.Driver
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:changeme}
    hikari:
      pool-name: QwikBankaPool
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      validation-timeout: 5000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1
      connection-init-sql: SET SESSION sql_mode='TRADITIONAL'
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false

# HIBERNATE CONFIGURATION
hibernate:
  cache:
    queries: false
    use_second_level_cache: false
    use_query_cache: false
  show_sql: false
  format_sql: false

# ENVIRONMENT SPECIFIC CONFIGURATIONS
environments:
  development:
    grails:
      serverURL: http://localhost:8080
    spring:
      datasource:
        dbCreate: create-drop
        url: **************************************
        hikari:
          maximum-pool-size: 10
          minimum-idle: 2
    logging:
      level:
        org.icbs: DEBUG
        org.springframework.security: DEBUG
        
  test:
    spring:
      datasource:
        dbCreate: update
        url: ***************************************
        hikari:
          maximum-pool-size: 5
          minimum-idle: 1
    logging:
      level:
        org.icbs: INFO
        
  production:
    grails:
      serverURL: ${SERVER_URL:https://qwikbanka.com}
      plugin:
        console:
          enabled: false
          fileStore:
            remote:
              enabled: false
    spring:
      datasource:
        dbCreate: none
        url: ${DB_URL:******************************************}
        hikari:
          maximum-pool-size: 50
          minimum-idle: 10
          connection-timeout: 30000
          validation-timeout: 10000
          leak-detection-threshold: 30000
    logging:
      level:
        org.icbs: INFO
        org.springframework.security: WARN
      file:
        name: /var/log/qwikbanka/application.log
        max-size: 100MB
        max-history: 30

# QUARTZ SCHEDULER
quartz:
  autoStartup: true

# APPLICATION INFO
info:
  app:
    name: '@info.app.name@'
    version: '@info.app.version@'
    grailsVersion: '@info.app.grailsVersion@'

# MANAGEMENT ENDPOINTS
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# SECURITY HEADERS AND CSRF
grails:
  plugin:
    springsecurity:
      csrf:
        enabled: true
      securityConfigType: 'Annotation'
      rejectIfNoRule: false
      fii:
        rejectPublicInvocations: false

# LOGGING CONFIGURATION
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  level:
    root: WARN
    org.springframework: WARN
    org.hibernate: WARN
    grails: INFO
