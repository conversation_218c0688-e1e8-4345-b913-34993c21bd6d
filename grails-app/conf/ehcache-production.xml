<?xml version="1.0" encoding="UTF-8"?>
<!--
QwikBanka Core Banking System - Production EhCache Configuration
Optimized for high-performance banking operations with appropriate cache sizes and TTL
-->
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd"
         updateCheck="false"
         monitoring="autodetect"
         dynamicConfig="true">

    <!-- Default cache configuration -->
    <defaultCache
        maxEntriesLocalHeap="10000"
        eternal="false"
        timeToIdleSeconds="300"
        timeToLiveSeconds="600"
        overflowToDisk="true"
        maxEntriesLocalDisk="********"
        diskPersistent="false"
        diskExpiryThreadIntervalSeconds="120"
        memoryStoreEvictionPolicy="LRU"
        statistics="true"/>

    <!-- Customer Information Cache -->
    <cache name="org.icbs.cif.Customer"
           maxEntriesLocalHeap="50000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="3600"
           overflowToDisk="true"
           diskPersistent="true"
           memoryStoreEvictionPolicy="LRU"
           statistics="true">
        <persistence strategy="localTempSwap"/>
    </cache>

    <!-- Deposit Account Cache -->
    <cache name="org.icbs.deposit.Deposit"
           maxEntriesLocalHeap="100000"
           eternal="false"
           timeToIdleSeconds="900"
           timeToLiveSeconds="1800"
           overflowToDisk="true"
           diskPersistent="true"
           memoryStoreEvictionPolicy="LRU"
           statistics="true">
        <persistence strategy="localTempSwap"/>
    </cache>

    <!-- Loan Account Cache -->
    <cache name="org.icbs.loans.Loan"
           maxEntriesLocalHeap="75000"
           eternal="false"
           timeToIdleSeconds="900"
           timeToLiveSeconds="1800"
           overflowToDisk="true"
           diskPersistent="true"
           memoryStoreEvictionPolicy="LRU"
           statistics="true">
        <persistence strategy="localTempSwap"/>
    </cache>

    <!-- User Master Cache -->
    <cache name="org.icbs.admin.UserMaster"
           maxEntriesLocalHeap="5000"
           eternal="false"
           timeToIdleSeconds="3600"
           timeToLiveSeconds="7200"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Branch Information Cache -->
    <cache name="org.icbs.admin.Branch"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="7200"
           timeToLiveSeconds="14400"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Product Configuration Cache -->
    <cache name="org.icbs.admin.Product"
           maxEntriesLocalHeap="2000"
           eternal="false"
           timeToIdleSeconds="3600"
           timeToLiveSeconds="7200"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- List of Values (LOV) Cache - Long TTL as these change infrequently -->
    <cache name="org.icbs.lov.ConfigItemStatus"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="14400"
           timeToLiveSeconds="28800"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Currency Cache -->
    <cache name="org.icbs.admin.Currency"
           maxEntriesLocalHeap="500"
           eternal="false"
           timeToIdleSeconds="7200"
           timeToLiveSeconds="14400"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Transaction Template Cache -->
    <cache name="org.icbs.admin.TxnTemplate"
           maxEntriesLocalHeap="2000"
           eternal="false"
           timeToIdleSeconds="3600"
           timeToLiveSeconds="7200"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Institution Parameters Cache -->
    <cache name="org.icbs.admin.Institution"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="7200"
           timeToLiveSeconds="14400"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Query Result Cache for frequently used queries -->
    <cache name="org.hibernate.cache.internal.StandardQueryCache"
           maxEntriesLocalHeap="5000"
           eternal="false"
           timeToIdleSeconds="300"
           timeToLiveSeconds="600"
           overflowToDisk="true"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Update timestamps cache -->
    <cache name="org.hibernate.cache.spi.UpdateTimestampsCache"
           maxEntriesLocalHeap="5000"
           eternal="true"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Session-based caches for user-specific data -->
    <cache name="userSessionCache"
           maxEntriesLocalHeap="10000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="3600"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Teller transaction cache for quick access -->
    <cache name="tellerTransactionCache"
           maxEntriesLocalHeap="20000"
           eternal="false"
           timeToIdleSeconds="300"
           timeToLiveSeconds="900"
           overflowToDisk="true"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Account balance cache for frequent inquiries -->
    <cache name="accountBalanceCache"
           maxEntriesLocalHeap="50000"
           eternal="false"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Interest rate cache -->
    <cache name="interestRateCache"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="3600"
           timeToLiveSeconds="7200"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Report cache for generated reports -->
    <cache name="reportCache"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="3600"
           overflowToDisk="true"
           diskPersistent="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true">
        <persistence strategy="localTempSwap"/>
    </cache>

    <!-- Audit log cache for recent entries -->
    <cache name="auditLogCache"
           maxEntriesLocalHeap="10000"
           eternal="false"
           timeToIdleSeconds="900"
           timeToLiveSeconds="1800"
           overflowToDisk="true"
           memoryStoreEvictionPolicy="FIFO"
           statistics="true"/>

    <!-- GL Account cache -->
    <cache name="glAccountCache"
           maxEntriesLocalHeap="5000"
           eternal="false"
           timeToIdleSeconds="3600"
           timeToLiveSeconds="7200"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

    <!-- Loan installment cache -->
    <cache name="loanInstallmentCache"
           maxEntriesLocalHeap="100000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="3600"
           overflowToDisk="true"
           diskPersistent="true"
           memoryStoreEvictionPolicy="LRU"
           statistics="true">
        <persistence strategy="localTempSwap"/>
    </cache>

    <!-- Security role and permission cache -->
    <cache name="securityCache"
           maxEntriesLocalHeap="2000"
           eternal="false"
           timeToIdleSeconds="3600"
           timeToLiveSeconds="7200"
           overflowToDisk="false"
           memoryStoreEvictionPolicy="LRU"
           statistics="true"/>

</ehcache>
