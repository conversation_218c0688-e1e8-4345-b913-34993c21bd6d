package org.icbs.security

import org.icbs.admin.Module
import org.icbs.admin.UserMaster
import org.icbs.admin.UserMessage
import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j

/**
 * Modern Menu Interceptor for QwikBanka Core Banking System
 * Replaces legacy MenuFilters with modern Grails 6.2.3 interceptor pattern
 * 
 * Features:
 * - High-performance menu caching
 * - Optimized database queries
 * - Modern interceptor patterns
 * - Comprehensive error handling
 * - Audit logging integration
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Slf4j
class MenuInterceptor {
    
    // Injected services
    def roleModuleService
    def policyService
    def cacheService
    def auditLogService
    
    // Cache configuration
    private static final String MENU_CACHE_PREFIX = "menu_"
    private static final String USER_MENU_CACHE_PREFIX = "user_menu_"
    private static final int CACHE_EXPIRY_MINUTES = 30
    
    /**
     * Constructor - Define interceptor scope
     * Apply to all controllers except excluded ones
     */
    MenuInterceptor() {
        matchAll()
            .excludes(controller: 'assets')
            .excludes(controller: 'ATMInterfaceListener')
            .excludes(action: 'login')
            .excludes(action: 'authenticate')
            .excludes(action: 'logout')
    }
    
    /**
     * Before interceptor - Not used for menu data
     */
    boolean before() {
        return true
    }
    
    /**
     * After interceptor - Add menu data to model
     * This is where we populate the menu data for views
     */
    boolean after() {
        try {
            // Skip if no model or no user session
            if (!model || !session.user_id) {
                return true
            }
            
            // Get current user
            Long userId = session.user_id as Long
            UserMaster currentUser = getUserFromCache(userId)
            
            if (!currentUser) {
                log.warn("User not found for session user_id: ${userId}")
                return true
            }
            
            // Add menu data to model with caching
            addMenuDataToModel(model, currentUser)
            
            // Add system information
            addSystemInfoToModel(model, currentUser)
            
            return true
            
        } catch (Exception e) {
            log.error("Error in MenuInterceptor.after(): ${e.message}", e)
            
            // Log audit event for debugging
            auditLogService?.logSystemEvent(
                'MENU_INTERCEPTOR_ERROR',
                "Error loading menu data: ${e.message}",
                session.user_id as String
            )
            
            // Don't break the request flow
            return true
        }
    }
    
    /**
     * After view interceptor - Not used
     */
    void afterView() {
        // No action needed
    }
    
    /**
     * Add menu data to model with caching
     */
    private void addMenuDataToModel(Map model, UserMaster currentUser) {
        String userCacheKey = "${USER_MENU_CACHE_PREFIX}${currentUser.id}"
        
        // Try to get menu data from cache
        Map menuData = cacheService?.get(userCacheKey)
        
        if (!menuData) {
            // Generate menu data
            menuData = generateMenuData(currentUser)
            
            // Cache the menu data
            cacheService?.put(userCacheKey, menuData, CACHE_EXPIRY_MINUTES)
        }
        
        // Add menu data to model
        model.putAll(menuData)
    }
    
    /**
     * Generate menu data for user
     */
    private Map generateMenuData(UserMaster currentUser) {
        Map menuData = [:]
        
        try {
            // Core banking module menus
            menuData.cifMenu = getModuleMenu('CIF00000')
            menuData.depositsMenu = getModuleMenu('DEP00000')
            menuData.loansMenu = getModuleMenu('LON00000')
            menuData.telleringMenu = getModuleMenu('TLR00000')
            menuData.glMenu = getModuleMenu('GEN00000')
            menuData.adminMenu = getModuleMenu('ADM00000')
            menuData.configMenu = getModuleMenu('CFG00000')
            menuData.auditMenu = getModuleMenu('AUD00000')
            
            // User-specific data
            menuData.unreadMessages = getUnreadMessageCount(currentUser)
            menuData.pendingPolicyExceptions = getPendingPolicyExceptionCount()
            menuData.runDate = currentUser.branch?.runDate
            menuData.subModules = getPermittedModules()
            
            return menuData
            
        } catch (Exception e) {
            log.error("Error generating menu data for user ${currentUser.id}: ${e.message}", e)
            return [:]
        }
    }
    
    /**
     * Get module menu with caching
     */
    private def getModuleMenu(String moduleCode) {
        String cacheKey = "${MENU_CACHE_PREFIX}${moduleCode}"
        
        def menu = cacheService?.get(cacheKey)
        if (!menu) {
            menu = roleModuleService?.getMenu(moduleCode)
            cacheService?.put(cacheKey, menu, CACHE_EXPIRY_MINUTES)
        }
        
        return menu
    }
    
    /**
     * Get unread message count with caching
     */
    private Integer getUnreadMessageCount(UserMaster currentUser) {
        String cacheKey = "unread_messages_${currentUser.id}"
        
        Integer count = cacheService?.get(cacheKey)
        if (count == null) {
            count = UserMessage.countByRecipientAndIsRead(currentUser, false)
            cacheService?.put(cacheKey, count, 5) // Cache for 5 minutes
        }
        
        return count
    }
    
    /**
     * Get pending policy exception count with caching
     */
    private Integer getPendingPolicyExceptionCount() {
        String cacheKey = "pending_policy_exceptions"
        
        Integer count = cacheService?.get(cacheKey)
        if (count == null) {
            count = policyService?.getPendingPolicyExceptionCount() ?: 0
            cacheService?.put(cacheKey, count, 10) // Cache for 10 minutes
        }
        
        return count
    }
    
    /**
     * Get permitted modules with caching
     */
    private def getPermittedModules() {
        String cacheKey = "permitted_modules_${session.user_id}"
        
        def modules = cacheService?.get(cacheKey)
        if (!modules) {
            modules = roleModuleService?.getPermittedModules()
            cacheService?.put(cacheKey, modules, CACHE_EXPIRY_MINUTES)
        }
        
        return modules
    }
    
    /**
     * Get user from cache or database
     */
    private UserMaster getUserFromCache(Long userId) {
        String cacheKey = "user_${userId}"
        
        UserMaster user = cacheService?.get(cacheKey)
        if (!user) {
            user = UserMaster.get(userId)
            if (user) {
                cacheService?.put(cacheKey, user, CACHE_EXPIRY_MINUTES)
            }
        }
        
        return user
    }
    
    /**
     * Add system information to model
     */
    private void addSystemInfoToModel(Map model, UserMaster currentUser) {
        try {
            // Add current user info
            model.currentUser = currentUser
            model.currentBranch = currentUser.branch
            model.currentUserRoles = currentUser.roles
            
            // Add system status
            model.systemDate = new Date()
            model.businessDate = currentUser.branch?.runDate
            
        } catch (Exception e) {
            log.error("Error adding system info to model: ${e.message}", e)
        }
    }
}
