package org.icbs.security

import org.icbs.admin.Module
import org.apache.commons.lang.StringUtils
import groovy.util.logging.Slf4j

/**
 * Modern Permission Interceptor for QwikBanka Core Banking System
 * Replaces legacy PermissionFilters with modern Grails 6.2.3 interceptor pattern
 * 
 * Features:
 * - Role-based access control (RBAC)
 * - Module-level permissions
 * - URI-based access control
 * - Comprehensive audit logging
 * - Performance optimized with caching
 * - Modern security patterns
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Slf4j
class PermissionInterceptor {
    
    // Injected services
    def roleModuleService
    def auditLogService
    def cacheService
    
    // Cache configuration
    private static final String MODULE_CACHE_PREFIX = "module_"
    private static final String PERMISSION_CACHE_PREFIX = "permission_"
    private static final int CACHE_EXPIRY_MINUTES = 30
    
    // Excluded actions that don't require permission checks
    private static final List<String> EXCLUDED_ACTIONS = [
        'login', 'authenticate', 'logout', 'landing', 
        'changePassword', 'saveChangePassword', 'health', 'error'
    ]
    
    /**
     * Constructor - Define interceptor scope
     * Apply to all controllers except assets
     */
    PermissionInterceptor() {
        matchAll()
            .excludes(controller: 'assets')
            .excludes(controller: 'error')
            .excludes(controller: 'health')
    }
    
    /**
     * Before interceptor - Check permissions
     */
    boolean before() {
        try {
            // Skip permission check for excluded actions
            if (isExcludedAction()) {
                return true
            }
            
            // Skip if no user session (handled by AuthenticationInterceptor)
            if (!session.user_id) {
                log.debug("No user session found, skipping permission check")
                return true
            }
            
            // Get the module for current request
            Module module = getModuleForRequest()
            
            if (!module) {
                // No module found - allow access (might be a new endpoint)
                log.debug("No module found for URI: ${getCleanRequestURI()}")
                return true
            }
            
            // Check if module is enabled
            if (!module.isOnMenu) {
                log.warn("Access denied to disabled module: ${module.name}")
                auditLogService?.logSecurityEvent(
                    'ACCESS_DENIED_DISABLED_MODULE',
                    "Access denied to disabled module: ${module.name}",
                    session.user_id as String,
                    request.remoteAddr
                )
                
                flash.error = "The ${module.name} module is currently disabled."
                redirect(controller: 'home', action: 'landing')
                return false
            }
            
            // Check user permissions for this module
            if (!hasPermissionForModule(module)) {
                log.warn("Access denied to module: ${module.name} for user: ${session.user_id}")
                auditLogService?.logSecurityEvent(
                    'ACCESS_DENIED_INSUFFICIENT_PERMISSIONS',
                    "Access denied to module: ${module.name}",
                    session.user_id as String,
                    request.remoteAddr
                )
                
                flash.error = "You are not authorized to access the ${module.name} module."
                redirect(controller: 'home', action: 'landing')
                return false
            }
            
            // Log successful access for audit
            auditLogService?.logSecurityEvent(
                'MODULE_ACCESS_GRANTED',
                "Access granted to module: ${module.name}",
                session.user_id as String,
                request.remoteAddr
            )
            
            return true
            
        } catch (Exception e) {
            log.error("Error in PermissionInterceptor.before(): ${e.message}", e)
            
            // Log security error
            auditLogService?.logSecurityEvent(
                'PERMISSION_CHECK_ERROR',
                "Error checking permissions: ${e.message}",
                session.user_id as String,
                request.remoteAddr
            )
            
            // Fail secure - deny access on error
            flash.error = "Security error occurred. Access denied."
            redirect(controller: 'home', action: 'landing')
            return false
        }
    }
    
    /**
     * After interceptor - Not used
     */
    boolean after() {
        return true
    }
    
    /**
     * After view interceptor - Not used
     */
    void afterView() {
        // No action needed
    }
    
    /**
     * Check if current action is excluded from permission checks
     */
    private boolean isExcludedAction() {
        String currentAction = actionName?.toLowerCase()
        return EXCLUDED_ACTIONS.contains(currentAction)
    }
    
    /**
     * Get module for current request with caching
     */
    private Module getModuleForRequest() {
        String cleanUri = getCleanRequestURI()
        String cacheKey = "${MODULE_CACHE_PREFIX}${cleanUri}"
        
        Module module = cacheService?.get(cacheKey)
        if (!module) {
            module = findModuleByUri(cleanUri)
            if (module) {
                cacheService?.put(cacheKey, module, CACHE_EXPIRY_MINUTES)
            }
        }
        
        return module
    }
    
    /**
     * Find module by URI with fallback logic
     */
    private Module findModuleByUri(String uri) {
        // Try exact match first
        Module module = Module.findByUri(uri)
        
        if (!module) {
            // Try with trailing slash
            module = Module.findByUri("${uri}/")
        }
        
        if (!module) {
            // Try without trailing slash
            String uriWithoutSlash = uri.endsWith('/') ? uri[0..-2] : uri
            module = Module.findByUri(uriWithoutSlash)
        }
        
        if (!module) {
            // Try parent path matching
            module = findModuleByParentPath(uri)
        }
        
        return module
    }
    
    /**
     * Find module by parent path for nested URIs
     */
    private Module findModuleByParentPath(String uri) {
        String[] pathParts = uri.split('/')
        
        // Try progressively shorter paths
        for (int i = pathParts.length - 1; i > 0; i--) {
            String parentPath = pathParts[0..i].join('/')
            Module module = Module.findByUri(parentPath)
            if (module) {
                return module
            }
        }
        
        return null
    }
    
    /**
     * Get clean request URI for module matching
     */
    private String getCleanRequestURI() {
        String uri = request.requestURI
        
        // Remove common prefixes and suffixes
        uri = uri.replace('.dispatch', '')
        uri = uri.replace('/icbs/', '/')
        uri = uri.replace('/grails/', '/')
        
        // Parse URI to get the relevant part
        String[] parts = uri.split('/')
        StringBuilder parsedUri = new StringBuilder()
        
        int slashCount = 0
        for (String part : parts) {
            if (part == '') {
                slashCount++
            }
            
            if (slashCount > 1) {
                parsedUri.append('/').append(part)
            }
        }
        
        String result = parsedUri.toString()
        
        // Remove trailing slash if present
        if (result.endsWith('/') && result.length() > 1) {
            result = result[0..-2]
        }
        
        return result
    }
    
    /**
     * Check if user has permission for module with caching
     */
    private boolean hasPermissionForModule(Module module) {
        String cacheKey = "${PERMISSION_CACHE_PREFIX}${session.user_id}_${module.id}"
        
        Boolean hasPermission = cacheService?.get(cacheKey)
        if (hasPermission == null) {
            hasPermission = roleModuleService?.hasPermission(module) ?: false
            cacheService?.put(cacheKey, hasPermission, CACHE_EXPIRY_MINUTES)
        }
        
        return hasPermission
    }
}
