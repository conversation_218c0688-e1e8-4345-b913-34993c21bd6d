package org.icbs.security

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Institution
import groovy.util.logging.Slf4j

/**
 * Modern Tellering Interceptor for QwikBanka Core Banking System
 * Replaces legacy TelleringFilters with modern Grails 6.2.3 interceptor pattern
 * 
 * Features:
 * - Teller session validation
 * - Branch operational status checks
 * - Business day validation
 * - Transaction limits enforcement
 * - Comprehensive audit logging
 * - Performance optimized with caching
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Slf4j
class TelleringInterceptor {
    
    // Injected services
    def auditLogService
    def cacheService
    def tellerSessionService
    def branchService
    
    // Cache configuration
    private static final String BRANCH_CACHE_PREFIX = "branch_status_"
    private static final String TELLER_CACHE_PREFIX = "teller_status_"
    private static final int CACHE_EXPIRY_MINUTES = 5 // Short cache for real-time data
    
    // Teller-related controllers that require validation
    private static final List<String> TELLER_CONTROLLERS = [
        'tellering', 'tellerCore', 'tellerPassbook', 'tellerCashTransaction',
        'tellerCheckTransaction', 'tellerDeposit', 'tellerLoanTransaction',
        'tellerForex', 'tellerBalancing', 'tellerReport', 'tellerUtility'
    ]
    
    /**
     * Constructor - Define interceptor scope
     * Apply only to teller-related controllers
     */
    TelleringInterceptor() {
        match(controller: ~/teller.*/)
    }
    
    /**
     * Before interceptor - Validate teller operations
     */
    boolean before() {
        try {
            // Skip if not a teller controller
            if (!isTellerController()) {
                return true
            }
            
            // Skip if no user session
            if (!session.user_id) {
                log.debug("No user session found for teller operation")
                return true
            }
            
            // Get current user and branch
            UserMaster currentUser = getCurrentUser()
            if (!currentUser) {
                log.error("User not found for session user_id: ${session.user_id}")
                flash.error = "Invalid user session. Please login again."
                redirect(controller: 'authentication', action: 'login')
                return false
            }
            
            Branch currentBranch = currentUser.branch
            if (!currentBranch) {
                log.error("No branch assigned to user: ${currentUser.userName}")
                flash.error = "No branch assigned to your account. Contact administrator."
                redirect(controller: 'home', action: 'landing')
                return false
            }
            
            // Validate teller operations are allowed
            if (!isTelleringActive(currentBranch)) {
                log.warn("Teller operations not allowed for branch: ${currentBranch.branchCode}")
                auditLogService?.logSecurityEvent(
                    'TELLER_ACCESS_DENIED_INACTIVE',
                    "Teller operations denied - branch inactive: ${currentBranch.branchCode}",
                    session.user_id as String,
                    request.remoteAddr
                )
                
                flash.error = "Teller transactions are not allowed at this time."
                redirect(controller: 'home', action: 'landing')
                return false
            }
            
            // Validate business day status
            if (!isValidBusinessDay(currentBranch)) {
                log.warn("Invalid business day for teller operations: ${currentBranch.runDate}")
                auditLogService?.logSecurityEvent(
                    'TELLER_ACCESS_DENIED_INVALID_BUSINESS_DAY',
                    "Teller operations denied - invalid business day",
                    session.user_id as String,
                    request.remoteAddr
                )
                
                flash.error = "Teller operations are not allowed. Invalid business day."
                redirect(controller: 'home', action: 'landing')
                return false
            }
            
            // Validate teller session
            if (!isValidTellerSession(currentUser)) {
                log.warn("Invalid teller session for user: ${currentUser.userName}")
                auditLogService?.logSecurityEvent(
                    'TELLER_ACCESS_DENIED_INVALID_SESSION',
                    "Teller operations denied - invalid teller session",
                    session.user_id as String,
                    request.remoteAddr
                )
                
                flash.error = "Invalid teller session. Please start your teller session."
                redirect(controller: 'tellerCore', action: 'startSession')
                return false
            }
            
            // Check system locks
            if (isSystemLocked()) {
                log.warn("System locked - teller operations denied")
                auditLogService?.logSecurityEvent(
                    'TELLER_ACCESS_DENIED_SYSTEM_LOCKED',
                    "Teller operations denied - system locked",
                    session.user_id as String,
                    request.remoteAddr
                )
                
                flash.error = "System is currently locked. Teller operations are not allowed."
                redirect(controller: 'home', action: 'landing')
                return false
            }
            
            // Log successful teller access
            auditLogService?.logTellerEvent(
                'TELLER_ACCESS_GRANTED',
                "Teller access granted for ${controllerName}.${actionName}",
                session.user_id as String,
                currentBranch.branchCode
            )
            
            // Add teller context to request
            addTellerContextToRequest(currentUser, currentBranch)
            
            return true
            
        } catch (Exception e) {
            log.error("Error in TelleringInterceptor.before(): ${e.message}", e)
            
            // Log security error
            auditLogService?.logSecurityEvent(
                'TELLER_VALIDATION_ERROR',
                "Error validating teller access: ${e.message}",
                session.user_id as String,
                request.remoteAddr
            )
            
            // Fail secure - deny access on error
            flash.error = "System error occurred. Teller operations denied."
            redirect(controller: 'home', action: 'landing')
            return false
        }
    }
    
    /**
     * After interceptor - Not used
     */
    boolean after() {
        return true
    }
    
    /**
     * After view interceptor - Not used
     */
    void afterView() {
        // No action needed
    }
    
    /**
     * Check if current controller is a teller controller
     */
    private boolean isTellerController() {
        String controller = controllerName?.toLowerCase()
        return TELLER_CONTROLLERS.any { controller?.startsWith(it) }
    }
    
    /**
     * Get current user with caching
     */
    private UserMaster getCurrentUser() {
        Long userId = session.user_id as Long
        String cacheKey = "user_${userId}"
        
        UserMaster user = cacheService?.get(cacheKey)
        if (!user) {
            user = UserMaster.get(userId)
            if (user) {
                cacheService?.put(cacheKey, user, CACHE_EXPIRY_MINUTES)
            }
        }
        
        return user
    }
    
    /**
     * Check if tellering is active for branch with caching
     */
    private boolean isTelleringActive(Branch branch) {
        String cacheKey = "${BRANCH_CACHE_PREFIX}${branch.id}"
        
        Boolean isActive = cacheService?.get(cacheKey)
        if (isActive == null) {
            // Refresh branch data
            branch.refresh()
            isActive = branch.isTelleringActive ?: false
            cacheService?.put(cacheKey, isActive, CACHE_EXPIRY_MINUTES)
        }
        
        return isActive
    }
    
    /**
     * Validate business day status
     */
    private boolean isValidBusinessDay(Branch branch) {
        try {
            Date currentDate = new Date()
            Date runDate = branch.runDate
            
            // Check if run date is current or future
            if (!runDate) {
                return false
            }
            
            // Allow same day or future dates
            return runDate >= currentDate.clearTime()
            
        } catch (Exception e) {
            log.error("Error validating business day: ${e.message}", e)
            return false
        }
    }
    
    /**
     * Validate teller session
     */
    private boolean isValidTellerSession(UserMaster user) {
        String cacheKey = "${TELLER_CACHE_PREFIX}${user.id}"
        
        Boolean isValid = cacheService?.get(cacheKey)
        if (isValid == null) {
            isValid = tellerSessionService?.isValidSession(user) ?: false
            cacheService?.put(cacheKey, isValid, CACHE_EXPIRY_MINUTES)
        }
        
        return isValid
    }
    
    /**
     * Check if system is locked
     */
    private boolean isSystemLocked() {
        String cacheKey = "system_lock_status"
        
        Boolean isLocked = cacheService?.get(cacheKey)
        if (isLocked == null) {
            Institution systemLock = Institution.findByParamCode('GEN.10250')
            isLocked = systemLock?.paramValue == 'TRUE'
            cacheService?.put(cacheKey, isLocked, 1) // Cache for 1 minute
        }
        
        return isLocked
    }
    
    /**
     * Add teller context to request for use in controllers
     */
    private void addTellerContextToRequest(UserMaster user, Branch branch) {
        request.setAttribute('tellerUser', user)
        request.setAttribute('tellerBranch', branch)
        request.setAttribute('tellerSession', session.teller_session_id)
        request.setAttribute('businessDate', branch.runDate)
        request.setAttribute('isTellerOperation', true)
    }
}
