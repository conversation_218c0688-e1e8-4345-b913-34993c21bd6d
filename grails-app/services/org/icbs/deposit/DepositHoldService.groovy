package org.icbs.deposit

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.deposit.Hold

/**
 * REFACTORED: DepositHoldService
 * Extracted from DepositService.groovy (2,035 lines)
 * Handles deposit hold operations with modern patterns
 * 
 * This service manages:
 * - Hold creation and management
 * - Hold amount calculations
 * - Hold status updates
 * - Hold validation and processing
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III (Service Decomposition)
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class DepositHoldService {
    
    // Service Dependencies
    def auditLogService
    
    /**
     * Save hold
     */
    def saveHold(Map params) {
        log.info("Saving hold for deposit account")
        
        return Hold.withTransaction { status ->
            def result = [:]
            def fail = { Map m ->
                status.setRollbackOnly()
                if (result.holdInstance && m.field) {
                    result.holdInstance.errors.rejectValue(m.field, m.code)
                }
                result.error = [code: m.code, args: ["Hold", params.id]]
                return result
            }
            
            try {
                result.holdInstance = new Hold()
                result.holdInstance.properties = params
                
                if (!result.holdInstance.validate()) {
                    return fail(code: "default.save.failure")
                }
                
                if (result.holdInstance.maturityDate <= Branch.get(1).runDate) {
                    return fail(code: "save.InvalidMaturityDate.fail")
                }
                
                // Do not allow cancelled or removed status for new hold
                if (result.holdInstance.status?.id > 2) {
                    return fail(code: "save.InvalidStatus.fail")  
                }
                
                def value
                if (result.holdInstance.type?.id == 1) {
                    // Fixed amount hold
                    value = result.holdInstance.amt
                } else if (result.holdInstance.type?.id == 2) {
                    // Percentage hold
                    value = result.holdInstance.deposit.availableBalAmt * (result.holdInstance.percent / 100)
                    result.holdInstance.amt = value
                }
                
                log.debug("Hold value calculated: ${value}")
                log.debug("Hold status: ${result.holdInstance.status?.id}")
                
                if (result.holdInstance.status?.id < 3) {
                    if (result.holdInstance.status?.id == 2) {
                        // Active hold
                        if (result.holdInstance.amt > result.holdInstance.deposit.availableBalAmt) {
                            return fail(code: "save.holdAmtGreaterThanAvailableBalance")
                        } 
                        
                        if (result.holdInstance.type?.id == 1) {
                            // Fixed amount hold
                            result.holdInstance.deposit.availableBalAmt -= value
                            result.holdInstance.deposit.holdBalAmt += value
                            result.holdInstance.deposit.save(validate: false, failOnError: true)
                        }
                        
                        if (result.holdInstance.type?.id == 2) {
                            // Percentage hold
                            result.holdInstance.deposit.availableBalAmt -= value
                            result.holdInstance.deposit.holdBalAmt += value
                            result.holdInstance.deposit.save(validate: false, failOnError: true)
                        }
                    }
                } else {
                    // Cancelled or removed hold
                    result.holdInstance.amt = value
                    result.holdInstance.deposit.availableBalAmt += value
                    result.holdInstance.deposit.holdBalAmt -= value
                    result.holdInstance.deposit.save(validate: false, failOnError: true)            
                }
                
                log.debug("Final hold status: ${result.holdInstance.status?.id}")
                result.holdInstance.save(failOnError: true)
                
                // Audit logging
                auditLogService.insert('080', 'DEP00801', 
                    "saveHold ${result.holdInstance.id}", 
                    'Hold', null, null, null, result.holdInstance.id)
                
                log.info("Hold saved successfully: ${result.holdInstance.id}")
                return result
                
            } catch (Exception e) {
                log.error("Error saving hold", e)
                return fail(code: "default.save.failure")
            }
        }
    }
    
    /**
     * Update hold
     */
    def updateHold(Map params, List includeList = null) {
        log.info("Updating hold: ${params.id}")
        
        return Hold.withTransaction { status ->
            def result = [:]
            def fail = { Map m ->
                status.setRollbackOnly()
                if (result.holdInstance && m.field) {
                    result.holdInstance.errors.rejectValue(m.field, m.code)
                }
                result.error = [code: m.code, args: ["Hold", params.id]]
                return result
            }
            
            try {
                result.holdInstance = Hold.get(params.id)
                log.debug("Hold instance: ${result.holdInstance}")
                
                if (!result.holdInstance) {
                    log.warn("Hold not found")
                    return fail(code: "default.not.found")
                }
                
                // Optimistic locking check
                if (params.version) {
                    log.debug("Version check")
                    if (result.holdInstance.version > params.version.toLong()) {
                        return fail(field: "version", code: "default.optimistic.locking.failure")
                    }
                }
                
                if (!includeList) {
                    result.holdInstance.properties = params
                } else {
                    result.holdInstance.properties[includeList] = params
                }
                
                if (!includeList) {
                    log.debug("Validating all properties")
                    if (!result.holdInstance.validate()) {    
                        return fail(code: "default.update.failure")
                    }
                } else {
                    log.debug("Validating specific properties")
                    if (!result.holdInstance.validate([includeList])) {    
                        return fail(code: "default.update.failure")
                    }
                }
                
                // Change in maturity date validation
                if (result.holdInstance.getPersistentValue('maturityDate') != result.holdInstance.maturityDate) {
                   if (result.holdInstance.maturityDate <= Branch.get(1).runDate) {
                       return fail(code: "default.update.failure-invalid Maturity Date") 
                   }  
                }            
                
                // Do not allow active hold to become pending
                if (result.holdInstance.getPersistentValue('status')?.id == 2 && result.holdInstance.status?.id == 1) {
                   return fail(code: "default.update.failure-Cannot set to pending") 
                }
                
                // Handle status changes that affect balances
                def oldStatus = result.holdInstance.getPersistentValue('status')?.id
                def newStatus = result.holdInstance.status?.id
                def holdAmount = result.holdInstance.amt
                
                if (oldStatus != newStatus) {
                    if (oldStatus == 2 && newStatus == 3) {
                        // Active to cancelled - release hold
                        result.holdInstance.deposit.availableBalAmt += holdAmount
                        result.holdInstance.deposit.holdBalAmt -= holdAmount
                        result.holdInstance.deposit.save(validate: false, failOnError: true)
                    } else if (oldStatus == 1 && newStatus == 2) {
                        // Pending to active - apply hold
                        if (holdAmount > result.holdInstance.deposit.availableBalAmt) {
                            return fail(code: "update.holdAmtGreaterThanAvailableBalance")
                        }
                        result.holdInstance.deposit.availableBalAmt -= holdAmount
                        result.holdInstance.deposit.holdBalAmt += holdAmount
                        result.holdInstance.deposit.save(validate: false, failOnError: true)
                    }
                }
                
                result.holdInstance.save()
                
                // Audit logging
                auditLogService.insert('080', 'DEP00802', 
                    "updateHold ${result.holdInstance.id}", 
                    'Hold', null, null, null, result.holdInstance.id)
                
                log.info("Hold updated successfully: ${result.holdInstance.id}")
                return result
                
            } catch (Exception e) {
                log.error("Error updating hold", e)
                return fail(code: "default.update.failure")
            }
        }
    }
    
    /**
     * Release hold
     */
    def releaseHold(Long holdId, String reason) {
        log.info("Releasing hold: ${holdId}")
        
        try {
            Hold hold = Hold.get(holdId)
            if (!hold) {
                return [success: false, error: "Hold not found"]
            }
            
            if (hold.status?.id != 2) {
                return [success: false, error: "Only active holds can be released"]
            }
            
            // Release the hold amount
            hold.deposit.availableBalAmt += hold.amt
            hold.deposit.holdBalAmt -= hold.amt
            hold.deposit.save(validate: false, failOnError: true)
            
            // Update hold status
            hold.status = org.icbs.lov.ConfigItemStatus.get(3) // Released/Cancelled
            hold.releaseReason = reason
            hold.releaseDate = new Date()
            hold.save(failOnError: true)
            
            // Audit logging
            auditLogService.insert('080', 'DEP00803', 
                "releaseHold ${hold.id} - Reason: ${reason}", 
                'Hold', null, null, null, hold.id)
            
            log.info("Hold released successfully: ${hold.id}")
            return [success: true, hold: hold]
            
        } catch (Exception e) {
            log.error("Error releasing hold", e)
            return [success: false, error: "Failed to release hold"]
        }
    }
    
    /**
     * Get holds for deposit account
     */
    List getHoldsForAccount(Long depositId) {
        try {
            return Hold.createCriteria().list {
                eq("deposit.id", depositId)
                order("dateCreated", "desc")
            }
        } catch (Exception e) {
            log.error("Error getting holds for account", e)
            return []
        }
    }
    
    /**
     * Get active holds for deposit account
     */
    List getActiveHoldsForAccount(Long depositId) {
        try {
            return Hold.createCriteria().list {
                eq("deposit.id", depositId)
                eq("status.id", 2) // Active status
                order("dateCreated", "desc")
            }
        } catch (Exception e) {
            log.error("Error getting active holds for account", e)
            return []
        }
    }
    
    /**
     * Calculate total hold amount for account
     */
    BigDecimal getTotalHoldAmount(Long depositId) {
        try {
            def result = Hold.createCriteria().get {
                eq("deposit.id", depositId)
                eq("status.id", 2) // Active status
                projections {
                    sum("amt")
                }
            }
            return result ?: 0.0
        } catch (Exception e) {
            log.error("Error calculating total hold amount", e)
            return 0.0
        }
    }
    
    /**
     * Validate hold amount against available balance
     */
    Map validateHoldAmount(Long depositId, BigDecimal holdAmount) {
        try {
            def deposit = org.icbs.deposit.Deposit.get(depositId)
            if (!deposit) {
                return [isValid: false, error: "Deposit account not found"]
            }
            
            if (holdAmount > deposit.availableBalAmt) {
                return [
                    isValid: false, 
                    error: "Hold amount exceeds available balance",
                    availableBalance: deposit.availableBalAmt,
                    requestedAmount: holdAmount
                ]
            }
            
            return [isValid: true]
            
        } catch (Exception e) {
            log.error("Error validating hold amount", e)
            return [isValid: false, error: "Validation error"]
        }
    }
}
