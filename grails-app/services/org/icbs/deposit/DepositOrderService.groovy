package org.icbs.deposit

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.deposit.StandingOrder
import org.icbs.deposit.StopPaymentOrder
import org.icbs.deposit.Cheque

/**
 * REFACTORED: DepositOrderService
 * Extracted from DepositService.groovy (2,035 lines)
 * Handles deposit order operations with modern patterns
 * 
 * This service manages:
 * - Standing orders
 * - Stop payment orders
 * - Order validation and processing
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III (Service Decomposition)
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class DepositOrderService {
    
    // Service Dependencies
    def auditLogService
    
    /**
     * Save standing order
     */
    def saveStandingOrder(Map params) {
        log.info("Saving standing order")
        
        return StandingOrder.withTransaction { status ->
            def result = [:]
            def fail = { Map m ->
                status.setRollbackOnly()
                if (result.standingOrderInstance && m.field) {
                    result.standingOrderInstance.errors.rejectValue(m.field, m.code)
                }
                result.error = [code: m.code, args: ["StandingOrder", params.id]]
                return result
            }
            
            try {
                result.standingOrderInstance = new StandingOrder()
                result.standingOrderInstance.properties = params
                
                if (!result.standingOrderInstance.validate()) {
                    return fail(code: "default.save.failure")
                }
                
                // Check if same account
                if (params.fundedDeposit == params.fundingDeposit) {
                    log.warn("Duplicate account in standing order")
                    return fail(code: "default.save.failure-duplicate Account")               
                }            
                
                result.standingOrderInstance.save(failOnError: true)
                
                // Audit logging
                auditLogService.insert('080', 'DEP00901', 
                    "saveStandingOrder ${result.standingOrderInstance.id}", 
                    'StandingOrder', null, null, null, result.standingOrderInstance.id)
                
                log.info("Standing order saved successfully: ${result.standingOrderInstance.id}")
                return result
                
            } catch (Exception e) {
                log.error("Error saving standing order", e)
                return fail(code: "default.save.failure")
            }
        }
    }
    
    /**
     * Update standing order
     */
    def updateStandingOrder(Map params, List includeList = null) {
        log.info("Updating standing order: ${params.id}")
        
        return StandingOrder.withTransaction { status ->
            def result = [:]
            def fail = { Map m ->
                status.setRollbackOnly()
                if (result.standingOrderInstance && m.field) {
                    result.standingOrderInstance.errors.rejectValue(m.field, m.code)
                }
                result.error = [code: m.code, args: ["StandingOrder", params.id]]
                return result
            }
            
            try {
                result.standingOrderInstance = StandingOrder.get(params.id)
                if (!result.standingOrderInstance) {
                    return fail(code: "default.not.found")
                }
                
                // Optimistic locking check
                if (params.version) {
                    if (result.standingOrderInstance.version > params.version.toLong()) {
                        return fail(field: "version", code: "default.optimistic.locking.failure")
                    }
                }
                
                if (!includeList) {
                    result.standingOrderInstance.properties = params
                } else {
                    result.standingOrderInstance.properties[includeList] = params
                }
                
                if (!includeList) {
                    if (!result.standingOrderInstance.validate()) {    
                        return fail(code: "default.update.failure")
                    }
                } else {
                    if (!result.standingOrderInstance.validate([includeList])) {    
                        return fail(code: "default.update.failure")
                    }
                }
                
                result.standingOrderInstance.save()
                
                // Audit logging
                auditLogService.insert('080', 'DEP00902', 
                    "updateStandingOrder ${result.standingOrderInstance.id}", 
                    'StandingOrder', null, null, null, result.standingOrderInstance.id)
                
                log.info("Standing order updated successfully: ${result.standingOrderInstance.id}")
                return result
                
            } catch (Exception e) {
                log.error("Error updating standing order", e)
                return fail(code: "default.update.failure")
            }
        }
    }
    
    /**
     * Save stop payment order
     */
    def saveStopPaymentOrder(Map params) {
        log.info("Saving stop payment order")
        
        return StopPaymentOrder.withTransaction { status ->
            def result = [:]
            def fail = { Map m ->
                status.setRollbackOnly()
                if (result.stopPaymentOrderInstance && m.field) {
                    result.stopPaymentOrderInstance.errors.rejectValue(m.field, m.code)
                }
                result.error = [code: m.code, args: ["StopPaymentOrder", params.id]]
                return result
            }
            
            try {
                result.stopPaymentOrderInstance = new StopPaymentOrder()
                result.stopPaymentOrderInstance.properties = params
                
                if (!result.stopPaymentOrderInstance.validate()) {
                    return fail(code: "default.save.failure")
                }
                
                result.stopPaymentOrderInstance.cheque = Cheque.find { 
                    chequeNo == result.stopPaymentOrderInstance.chequeNo 
                }
                result.stopPaymentOrderInstance.stopAt = result.stopPaymentOrderInstance.deposit.branch.runDate
                result.stopPaymentOrderInstance.save()
                
                // Audit logging
                auditLogService.insert('080', 'DEP01401', 
                    "saveStopPaymentOrder ${result.stopPaymentOrderInstance.id}", 
                    'StopPaymentOrder', null, null, null, result.stopPaymentOrderInstance.id)
                
                log.info("Stop payment order saved successfully: ${result.stopPaymentOrderInstance.id}")
                return result
                
            } catch (Exception e) {
                log.error("Error saving stop payment order", e)
                return fail(code: "default.save.failure")
            }
        }
    }
    
    /**
     * Update stop payment order
     */
    def updateStopPaymentOrder(Map params, List includeList = null) {
        log.info("Updating stop payment order: ${params.id}")
        
        return StopPaymentOrder.withTransaction { status ->
            def result = [:]
            def fail = { Map m ->
                status.setRollbackOnly()
                if (result.stopPaymentOrderInstance && m.field) {
                    result.stopPaymentOrderInstance.errors.rejectValue(m.field, m.code)
                }
                result.error = [code: m.code, args: ["StopPaymentOrder", params.id]]
                return result
            }
            
            try {
                result.stopPaymentOrderInstance = StopPaymentOrder.get(params.id)
                if (!result.stopPaymentOrderInstance) {
                    return fail(code: "default.not.found")
                }
                
                // Optimistic locking check
                if (params.version) {
                    if (result.stopPaymentOrderInstance.version > params.version.toLong()) {
                        return fail(field: "version", code: "default.optimistic.locking.failure")
                    }
                }
                
                if (!includeList) {
                    result.stopPaymentOrderInstance.properties = params
                } else {
                    result.stopPaymentOrderInstance.properties[includeList] = params
                }
                
                if (!includeList) {
                    if (!result.stopPaymentOrderInstance.validate()) {    
                        return fail(code: "default.update.failure")
                    }
                } else {
                    if (!result.stopPaymentOrderInstance.validate([includeList])) {    
                        return fail(code: "default.update.failure")
                    }
                }
                
                result.stopPaymentOrderInstance.save()
                
                // Audit logging
                auditLogService.insert('080', 'DEP01402', 
                    "updateStopPaymentOrder ${result.stopPaymentOrderInstance.id}", 
                    'StopPaymentOrder', null, null, null, result.stopPaymentOrderInstance.id)
                
                log.info("Stop payment order updated successfully: ${result.stopPaymentOrderInstance.id}")
                return result
                
            } catch (Exception e) {
                log.error("Error updating stop payment order", e)
                return fail(code: "default.update.failure")
            }
        }
    }
    
    /**
     * Get standing orders for deposit account
     */
    List getStandingOrdersForAccount(Long depositId) {
        try {
            return StandingOrder.createCriteria().list {
                or {
                    eq("fundingDeposit.id", depositId)
                    eq("fundedDeposit.id", depositId)
                }
                order("dateCreated", "desc")
            }
        } catch (Exception e) {
            log.error("Error getting standing orders for account", e)
            return []
        }
    }
    
    /**
     * Get stop payment orders for deposit account
     */
    List getStopPaymentOrdersForAccount(Long depositId) {
        try {
            return StopPaymentOrder.createCriteria().list {
                eq("deposit.id", depositId)
                order("stopAt", "desc")
            }
        } catch (Exception e) {
            log.error("Error getting stop payment orders for account", e)
            return []
        }
    }
    
    /**
     * Cancel standing order
     */
    def cancelStandingOrder(Long standingOrderId, String reason) {
        try {
            StandingOrder standingOrder = StandingOrder.get(standingOrderId)
            if (!standingOrder) {
                return [success: false, error: "Standing order not found"]
            }
            
            standingOrder.status = org.icbs.lov.ConfigItemStatus.get(3) // Cancelled
            standingOrder.cancelReason = reason
            standingOrder.cancelDate = new Date()
            standingOrder.save(flush: true, failOnError: true)
            
            // Audit logging
            auditLogService.insert('080', 'DEP00903', 
                "cancelStandingOrder ${standingOrder.id} - Reason: ${reason}", 
                'StandingOrder', null, null, null, standingOrder.id)
            
            log.info("Standing order cancelled successfully: ${standingOrder.id}")
            return [success: true, standingOrder: standingOrder]
            
        } catch (Exception e) {
            log.error("Error cancelling standing order", e)
            return [success: false, error: "Failed to cancel standing order"]
        }
    }
    
    /**
     * Cancel stop payment order
     */
    def cancelStopPaymentOrder(Long stopPaymentOrderId, String reason) {
        try {
            StopPaymentOrder stopPaymentOrder = StopPaymentOrder.get(stopPaymentOrderId)
            if (!stopPaymentOrder) {
                return [success: false, error: "Stop payment order not found"]
            }
            
            stopPaymentOrder.status = org.icbs.lov.ConfigItemStatus.get(3) // Cancelled
            stopPaymentOrder.cancelReason = reason
            stopPaymentOrder.cancelDate = new Date()
            stopPaymentOrder.save(flush: true, failOnError: true)
            
            // Audit logging
            auditLogService.insert('080', 'DEP01403', 
                "cancelStopPaymentOrder ${stopPaymentOrder.id} - Reason: ${reason}", 
                'StopPaymentOrder', null, null, null, stopPaymentOrder.id)
            
            log.info("Stop payment order cancelled successfully: ${stopPaymentOrder.id}")
            return [success: true, stopPaymentOrder: stopPaymentOrder]
            
        } catch (Exception e) {
            log.error("Error cancelling stop payment order", e)
            return [success: false, error: "Failed to cancel stop payment order"]
        }
    }
}
