package org.icbs.domain

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async

/**
 * Domain Event Service for QwikBanka Core Banking System
 * Handles domain events for audit logging, business rule enforcement, and system integration
 * 
 * Features:
 * - Asynchronous event processing
 * - Audit trail generation
 * - Business rule enforcement
 * - System integration events
 * - Performance monitoring
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Slf4j
@Transactional
class DomainEventService {
    
    def auditLogService
    def cacheService
    def notificationService
    
    /**
     * Handle customer-related domain events
     */
    @EventListener
    @Async
    void handleCustomerEvent(DomainEvent event) {
        if (!event.domainObject instanceof org.icbs.cif.Customer) {
            return
        }
        
        def customer = event.domainObject
        
        try {
            switch (event.eventType) {
                case 'CUSTOMER_CREATED':
                    handleCustomerCreated(customer, event.additionalData)
                    break
                case 'CUSTOMER_UPDATED':
                    handleCustomerUpdated(customer, event.additionalData)
                    break
                case 'CUSTOMER_STATUS_CHANGED':
                    handleCustomerStatusChanged(customer, event.additionalData)
                    break
                case 'CUSTOMER_DELETED':
                    handleCustomerDeleted(customer, event.additionalData)
                    break
                default:
                    log.debug("Unhandled customer event: ${event.eventType}")
            }
            
        } catch (Exception e) {
            log.error("Error handling customer event ${event.eventType}: ${e.message}", e)
        }
    }
    
    /**
     * Handle deposit account domain events
     */
    @EventListener
    @Async
    void handleDepositEvent(DomainEvent event) {
        if (!event.domainObject.class.simpleName == 'Deposit') {
            return
        }
        
        def deposit = event.domainObject
        
        try {
            switch (event.eventType) {
                case 'DEPOSIT_CREATED':
                    handleDepositCreated(deposit, event.additionalData)
                    break
                case 'DEPOSIT_BALANCE_UPDATED':
                    handleDepositBalanceUpdated(deposit, event.additionalData)
                    break
                case 'DEPOSIT_STATUS_CHANGED':
                    handleDepositStatusChanged(deposit, event.additionalData)
                    break
                case 'DEPOSIT_DORMANT':
                    handleDepositDormant(deposit, event.additionalData)
                    break
                default:
                    log.debug("Unhandled deposit event: ${event.eventType}")
            }
            
        } catch (Exception e) {
            log.error("Error handling deposit event ${event.eventType}: ${e.message}", e)
        }
    }
    
    /**
     * Handle loan account domain events
     */
    @EventListener
    @Async
    void handleLoanEvent(DomainEvent event) {
        if (!event.domainObject.class.simpleName == 'Loan') {
            return
        }
        
        def loan = event.domainObject
        
        try {
            switch (event.eventType) {
                case 'LOAN_CREATED':
                    handleLoanCreated(loan, event.additionalData)
                    break
                case 'LOAN_PAYMENT_RECEIVED':
                    handleLoanPaymentReceived(loan, event.additionalData)
                    break
                case 'LOAN_OVERDUE':
                    handleLoanOverdue(loan, event.additionalData)
                    break
                case 'LOAN_MATURED':
                    handleLoanMatured(loan, event.additionalData)
                    break
                default:
                    log.debug("Unhandled loan event: ${event.eventType}")
            }
            
        } catch (Exception e) {
            log.error("Error handling loan event ${event.eventType}: ${e.message}", e)
        }
    }
    
    /**
     * Handle transaction domain events
     */
    @EventListener
    @Async
    void handleTransactionEvent(DomainEvent event) {
        if (!event.domainObject.class.simpleName == 'TxnFile') {
            return
        }
        
        def transaction = event.domainObject
        
        try {
            switch (event.eventType) {
                case 'TRANSACTION_CREATED':
                    handleTransactionCreated(transaction, event.additionalData)
                    break
                case 'TRANSACTION_COMPLETED':
                    handleTransactionCompleted(transaction, event.additionalData)
                    break
                case 'TRANSACTION_FAILED':
                    handleTransactionFailed(transaction, event.additionalData)
                    break
                case 'LARGE_TRANSACTION':
                    handleLargeTransaction(transaction, event.additionalData)
                    break
                default:
                    log.debug("Unhandled transaction event: ${event.eventType}")
            }
            
        } catch (Exception e) {
            log.error("Error handling transaction event ${event.eventType}: ${e.message}", e)
        }
    }
    
    // ========================================
    // CUSTOMER EVENT HANDLERS
    // ========================================
    
    private void handleCustomerCreated(def customer, Map additionalData) {
        log.info("Processing customer created event for customer: ${customer.customerId}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'CUSTOMER_CREATED',
            "New customer created: ${customer.displayName}",
            additionalData.userId as String,
            'Customer',
            customer.id
        )
        
        // Clear customer cache
        cacheService?.evictPattern("customer_*")
        
        // Send welcome notification
        notificationService?.sendWelcomeNotification(customer)
    }
    
    private void handleCustomerUpdated(def customer, Map additionalData) {
        log.info("Processing customer updated event for customer: ${customer.customerId}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'CUSTOMER_UPDATED',
            "Customer information updated: ${customer.displayName}",
            additionalData.userId as String,
            'Customer',
            customer.id
        )
        
        // Clear customer cache
        cacheService?.evict("customer_${customer.id}")
        cacheService?.evictPattern("customer_search_*")
    }
    
    private void handleCustomerStatusChanged(def customer, Map additionalData) {
        log.info("Processing customer status change for customer: ${customer.customerId}")
        
        def oldStatus = additionalData.oldStatus
        def newStatus = customer.status
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'CUSTOMER_STATUS_CHANGED',
            "Customer status changed from ${oldStatus?.description} to ${newStatus?.description}",
            additionalData.userId as String,
            'Customer',
            customer.id
        )
        
        // Handle specific status changes
        if (newStatus?.id == 3) { // Blocked
            handleCustomerBlocked(customer, additionalData)
        } else if (newStatus?.id == 4) { // Closed
            handleCustomerClosed(customer, additionalData)
        }
    }
    
    private void handleCustomerDeleted(def customer, Map additionalData) {
        log.warn("Processing customer deletion for customer: ${customer.customerId}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'CUSTOMER_DELETED',
            "Customer deleted: ${customer.displayName}",
            additionalData.userId as String,
            'Customer',
            customer.id
        )
        
        // Clear all customer-related caches
        cacheService?.evictPattern("customer_*")
    }
    
    // ========================================
    // DEPOSIT EVENT HANDLERS
    // ========================================
    
    private void handleDepositCreated(def deposit, Map additionalData) {
        log.info("Processing deposit created event for account: ${deposit.acctNo}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'DEPOSIT_CREATED',
            "New deposit account created: ${deposit.acctNo}",
            additionalData.userId as String,
            'Deposit',
            deposit.id
        )
        
        // Clear deposit cache
        cacheService?.evictPattern("deposit_*")
        
        // Send account opening notification
        notificationService?.sendAccountOpeningNotification(deposit)
    }
    
    private void handleDepositBalanceUpdated(def deposit, Map additionalData) {
        log.debug("Processing deposit balance update for account: ${deposit.acctNo}")
        
        def oldBalance = additionalData.oldBalance
        def newBalance = deposit.availableBalance
        
        // Create audit log for significant balance changes
        if (Math.abs(newBalance - oldBalance) > 10000) {
            auditLogService?.logBusinessEvent(
                'DEPOSIT_LARGE_BALANCE_CHANGE',
                "Large balance change: ${oldBalance} to ${newBalance}",
                additionalData.userId as String,
                'Deposit',
                deposit.id
            )
        }
        
        // Clear balance cache
        cacheService?.evict("deposit_balance_${deposit.id}")
    }
    
    private void handleDepositStatusChanged(def deposit, Map additionalData) {
        log.info("Processing deposit status change for account: ${deposit.acctNo}")
        
        def oldStatus = additionalData.oldStatus
        def newStatus = deposit.status
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'DEPOSIT_STATUS_CHANGED',
            "Deposit status changed from ${oldStatus?.description} to ${newStatus?.description}",
            additionalData.userId as String,
            'Deposit',
            deposit.id
        )
    }
    
    private void handleDepositDormant(def deposit, Map additionalData) {
        log.warn("Processing deposit dormancy for account: ${deposit.acctNo}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'DEPOSIT_DORMANT',
            "Deposit account marked as dormant: ${deposit.acctNo}",
            'SYSTEM',
            'Deposit',
            deposit.id
        )
        
        // Send dormancy notification
        notificationService?.sendDormancyNotification(deposit)
    }
    
    // ========================================
    // LOAN EVENT HANDLERS
    // ========================================
    
    private void handleLoanCreated(def loan, Map additionalData) {
        log.info("Processing loan created event for account: ${loan.accountNo}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'LOAN_CREATED',
            "New loan account created: ${loan.accountNo}",
            additionalData.userId as String,
            'Loan',
            loan.id
        )
        
        // Clear loan cache
        cacheService?.evictPattern("loan_*")
        
        // Send loan approval notification
        notificationService?.sendLoanApprovalNotification(loan)
    }
    
    private void handleLoanPaymentReceived(def loan, Map additionalData) {
        log.info("Processing loan payment for account: ${loan.accountNo}")
        
        def paymentAmount = additionalData.paymentAmount
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'LOAN_PAYMENT_RECEIVED',
            "Loan payment received: ${paymentAmount}",
            additionalData.userId as String,
            'Loan',
            loan.id
        )
        
        // Clear loan balance cache
        cacheService?.evict("loan_balance_${loan.id}")
    }
    
    private void handleLoanOverdue(def loan, Map additionalData) {
        log.warn("Processing loan overdue for account: ${loan.accountNo}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'LOAN_OVERDUE',
            "Loan payment overdue: ${loan.accountNo}",
            'SYSTEM',
            'Loan',
            loan.id
        )
        
        // Send overdue notification
        notificationService?.sendOverdueNotification(loan)
    }
    
    private void handleLoanMatured(def loan, Map additionalData) {
        log.info("Processing loan maturity for account: ${loan.accountNo}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'LOAN_MATURED',
            "Loan account matured: ${loan.accountNo}",
            'SYSTEM',
            'Loan',
            loan.id
        )
        
        // Send maturity notification
        notificationService?.sendLoanMaturityNotification(loan)
    }
    
    // ========================================
    // TRANSACTION EVENT HANDLERS
    // ========================================
    
    private void handleTransactionCreated(def transaction, Map additionalData) {
        log.debug("Processing transaction created event: ${transaction.referenceNo}")
        
        // Create audit log for large transactions
        if (transaction.txnAmt > 100000) {
            auditLogService?.logBusinessEvent(
                'LARGE_TRANSACTION_CREATED',
                "Large transaction created: ${transaction.txnAmt}",
                additionalData.userId as String,
                'TxnFile',
                transaction.id
            )
        }
    }
    
    private void handleTransactionCompleted(def transaction, Map additionalData) {
        log.debug("Processing transaction completed event: ${transaction.referenceNo}")
        
        // Clear transaction cache
        cacheService?.evict("transaction_${transaction.id}")
        
        // Update account balances cache
        if (transaction.acctNo) {
            cacheService?.evictPattern("*_balance_*")
        }
    }
    
    private void handleTransactionFailed(def transaction, Map additionalData) {
        log.warn("Processing transaction failed event: ${transaction.referenceNo}")
        
        def failureReason = additionalData.failureReason
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'TRANSACTION_FAILED',
            "Transaction failed: ${failureReason}",
            additionalData.userId as String,
            'TxnFile',
            transaction.id
        )
    }
    
    private void handleLargeTransaction(def transaction, Map additionalData) {
        log.info("Processing large transaction event: ${transaction.referenceNo}")
        
        // Create audit log
        auditLogService?.logBusinessEvent(
            'LARGE_TRANSACTION',
            "Large transaction processed: ${transaction.txnAmt}",
            additionalData.userId as String,
            'TxnFile',
            transaction.id
        )
        
        // Send large transaction notification
        notificationService?.sendLargeTransactionNotification(transaction)
    }
    
    // ========================================
    // HELPER METHODS
    // ========================================
    
    private void handleCustomerBlocked(def customer, Map additionalData) {
        log.warn("Customer blocked: ${customer.customerId}")
        
        // Send customer blocked notification
        notificationService?.sendCustomerBlockedNotification(customer)
        
        // Clear all customer-related caches
        cacheService?.evictPattern("customer_${customer.id}_*")
    }
    
    private void handleCustomerClosed(def customer, Map additionalData) {
        log.info("Customer closed: ${customer.customerId}")
        
        // Send customer closure notification
        notificationService?.sendCustomerClosureNotification(customer)
        
        // Archive customer data
        // archiveService?.archiveCustomerData(customer)
    }
}
