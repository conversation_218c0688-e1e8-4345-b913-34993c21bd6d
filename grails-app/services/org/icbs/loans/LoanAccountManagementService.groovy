package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.loans.Loan
import org.icbs.loans.LoanLossProvisionDetail
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanPerformanceClassification
import org.icbs.admin.Institution

/**
 * REFACTORED: LoanAccountManagementService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan account management operations with modern patterns
 * 
 * This service manages:
 * - Loan account number generation
 * - Loan account initialization
 * - Loan account status management
 * - Basic loan account operations
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanAccountManagementService {
    
    /**
     * Generate unique account number for loan
     * Uses checksum algorithm for validation
     */
    def generateAccountNo(Loan loanInstance) {
        log.info("Generating account number for loan: ${loanInstance?.id}")
        
        int[] piArray = [1, 4, 1, 5, 9, 2, 6, 5, 3, 5, 8]
        String accountNo
        String branchCode
        String productCode
        
        log.debug("Branch: ${loanInstance.branch}, Product: ${loanInstance.product}")
        
        // Get next serial number for branch-product combination
        def result = Loan.executeQuery(
            "select max(SUBSTRING(accountNo, 9,5)) from Loan where SUBSTRING(accountNo, 1,7) = CONCAT(:branch,'-',:prod))", 
            [
                branch: String.format("%03d", loanInstance?.branch?.code), 
                prod: String.format("%03d", loanInstance?.product?.code)
            ]
        )
        
        int serialNum
        String serialOld 
        if (result[0] != null) {
            serialOld = result[0]
            serialNum = Integer.parseInt(serialOld)
        } else {
            serialNum = 0
        } 

        serialNum++
        
        // Format components
        branchCode = String.format("%03d", loanInstance?.branch?.code)
        productCode = String.format("%03d", loanInstance?.product?.code)
        String serialCode = String.format("%05d", serialNum)

        // Generate base account number
        accountNo = branchCode + productCode + serialCode
        log.debug("Base account number: ${accountNo}")
        
        // Calculate checksum
        def checksum = 0
        for(int i = accountNo.length() - 1; i >= 0; i--) {
            checksum += Character.getNumericValue(accountNo.charAt(i)) * piArray[i]
        }
        
        String checkBit = (checksum % 10).toString()
        
        // Set final account number with formatting
        loanInstance.accountNo = branchCode + "-" + productCode + "-" + serialCode + "-" + checkBit
        loanInstance.openingDate = Branch?.get(1).runDate
        
        log.info("Generated account number: ${loanInstance.accountNo}")
        loanInstance.save(failOnError: true)
        
        return loanInstance.accountNo
    }

    /**
     * Initialize loan instance with default values
     */
    def initializeLoan(Loan loanInstance) {
        log.info("Initializing loan: ${loanInstance?.accountNo}")
        
        // Set initial status
        loanInstance.status = LoanAcctStatus.get(1)
        loanInstance.performanceClassification = LoanPerformanceClassification.get(1)

        // Reset balances for new loans only
        if (loanInstance.status.id <= 2) {
            loanInstance.balanceAmount = 0
            loanInstance.totalDisbursementAmount = 0         
            loanInstance.transactionSequenceNo = 0 
            loanInstance.normalInterestAmount = 0
            loanInstance.interestBalanceAmount = 0
            loanInstance.penaltyBalanceAmount = 0
            loanInstance.serviceChargeBalanceAmount = 0
            loanInstance.taxBalanceAmount = 0
            loanInstance.accruedInterestAmount = 0
        } 

        // Set interest accrual flag
        loanInstance.hasInterestAccrual = loanInstance?.interestIncomeScheme?.hasInterestAccrual ?: false
        
        log.debug("Loan initialized with status: ${loanInstance.status.description}")
    }

    /**
     * Update loan status with proper validation
     */
    def updateStatus(Loan loanInstance, LoanAcctStatus status) {
        log.info("Updating loan status from ${loanInstance.status.description} to ${status.description}")
        
        def oldStatus = loanInstance.status
        loanInstance.status = status
        
        // Set status change date for closed loans
        if (status.id == 6) {
            loanInstance.statusChangedDate = loanInstance.branch.runDate
        }
        
        loanInstance.save(flush: true)
        
        log.info("Loan status updated successfully for account: ${loanInstance.accountNo}")
        return [oldStatus: oldStatus, newStatus: status]
    }

    /**
     * Update opening balance for loan account
     */
    def updateOpeningBalance(Loan loanInstance) {
        log.info("Updating opening balance for loan: ${loanInstance.accountNo}")
        
        def granted = loanInstance.grantedAmount
        def net = loanInstance.totalNetProceeds
        def balance = granted - net 
        
        log.debug("Granted: ${granted}, Net: ${net}, Balance: ${balance}")
        
        loanInstance.balanceAmount = balance
        loanInstance.save(flush: true)
        
        log.info("Opening balance updated to: ${balance}")
        return balance
    }

    /**
     * Create loan loss provision details
     */
    def createLoanLossProvision(Loan loanInstance) {
        log.info("Creating loan loss provision for: ${loanInstance.accountNo}")
        
        def existingProvision = LoanLossProvisionDetail.findByLoan(loanInstance)
        if (!existingProvision) {
            def newProvision = new LoanLossProvisionDetail(loan: loanInstance)
            newProvision.save(flush: true, failOnError: true)
            
            log.info("Loan loss provision created successfully")
            return newProvision
        } else {
            log.debug("Loan loss provision already exists")
            return existingProvision
        }
    }

    /**
     * Set promissory note number if enabled
     */
    def setPromissoryNoteNumber(Loan loanInstance) {
        def pnSetting = Institution.findByParamCode('LNS.50100')
        if (pnSetting?.paramValue == 'TRUE') {
            loanInstance.pnNo = loanInstance.accountNo
            loanInstance.save(flush: true)
            
            log.info("Promissory note number set: ${loanInstance.pnNo}")
        }
    }

    /**
     * Validate loan application before account creation
     */
    def validateLoanApplication(Loan loanInstance) {
        log.info("Validating loan application for account creation")
        
        if (loanInstance.loanApplication == null) {
            throw new IllegalArgumentException("Loan application is required")
        }
        
        def approvalStatus = loanInstance.loanApplication.approvalStatus
        if (!(approvalStatus.id in [6, 9, 10, 11])) {
            throw new IllegalStateException("Loan application must be approved before creating loan account")
        }
        
        if (loanInstance.grantedAmount < 0) {
            throw new IllegalArgumentException("Loan amount cannot be negative")
        }
        
        log.info("Loan application validation passed")
        return true
    }

    /**
     * Set default values before validation
     */
    def beforeValidation(Loan loanInstance) {
        log.debug("Setting default values before validation")
        
        if (loanInstance.loanApplication) {       
            // Set branch if not specified
            if (loanInstance.branch == null) {
                loanInstance.branch = loanInstance?.loanApplication?.branch
            }
            
            // Set currency from product
            loanInstance.currency = loanInstance?.product?.currency

            // Handle installment calculation type
            if (loanInstance?.interestIncomeScheme?.installmentCalcType.id == 1) {
                loanInstance.numInstallments = 1
                loanInstance.frequency = LoanInstallmentFreq.get(1)
            } else {
                loanInstance.term = 0
            }

            // Handle penalty scheme type
            if (loanInstance?.currentPenaltyScheme?.type.id == 1) {  // fixed amount
                loanInstance.penaltyRate = 0
            } else if (loanInstance.currentPenaltyScheme?.type.id == 2) {  // rate based
                loanInstance.penaltyAmount = 0
            }

            // Handle advanced interest settings
            handleAdvancedInterestSettings(loanInstance)
        }
        
        log.debug("Default values set successfully")
    }

    /**
     * Handle advanced interest settings based on type
     */
    private def handleAdvancedInterestSettings(Loan loanInstance) {
        def advInterestType = loanInstance?.interestIncomeScheme?.advInterestType.id
        
        if (advInterestType == 1) {  // none
            loanInstance.advInterestPeriods = 0
            loanInstance.advInterestDays = 0
        } else if (advInterestType == 2) {  // full
            if (loanInstance?.interestIncomeScheme?.installmentCalcType.id == 1) {
                loanInstance.advInterestPeriods = 0
                loanInstance.advInterestDays = loanInstance.term
            } else if (loanInstance?.interestIncomeScheme?.installmentCalcType.id == 5) {
                loanInstance.advInterestPeriods = loanInstance.numInstallments
                loanInstance.advInterestDays = 0
            } else {
                loanInstance.advInterestPeriods = 0
                loanInstance.advInterestDays = 0
            }
        } else if (advInterestType == 3) {  // partial
            if (loanInstance?.interestIncomeScheme?.installmentCalcType.id == 1) {
                loanInstance.advInterestPeriods = 0
            } else if (loanInstance?.interestIncomeScheme?.installmentCalcType.id in [2, 5]) {
                loanInstance.advInterestDays = 0
            } else {
                loanInstance.advInterestPeriods = 0
                loanInstance.advInterestDays = 0
            }
        }
    }

    /**
     * Get maturity date for loan
     */
    def getMaturityDate(Loan loanInstance) {
        if (loanInstance.term && loanInstance.openingDate) {
            return loanInstance.openingDate + loanInstance.term
        }
        return null
    }
}
