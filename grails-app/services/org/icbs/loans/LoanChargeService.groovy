package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import javax.servlet.http.HttpSession
import org.springframework.web.context.request.RequestContextHolder

import org.icbs.loans.Loan
import org.icbs.loans.LoanServiceCharge
import org.icbs.loans.LoanDeduction
import org.icbs.loans.LoanSweepAccount

/**
 * REFACTORED: LoanChargeService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan charges, deductions, and fees with modern patterns
 * 
 * This service manages:
 * - Service charges management
 * - Loan deductions processing
 * - Sweep accounts handling
 * - Fee calculations and applications
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanChargeService {
    
    /**
     * Save service charges from session to loan
     */
    def saveServiceCharges(Loan loanInstance) {
        log.info("Saving service charges for loan: ${loanInstance?.accountNo}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            def serviceCharges = session["serviceCharges"]
            
            if (serviceCharges) {
                def totalCharges = 0.00D
                
                for (serviceCharge in serviceCharges) {
                    loanInstance.addToServiceCharges(serviceCharge)
                    totalCharges += serviceCharge.amount ?: 0.00D
                    
                    log.debug("Added service charge: ${serviceCharge.description} - ${serviceCharge.amount}")
                }
                
                // Clear session after processing
                session["serviceCharges"] = null
                
                log.info("Service charges saved successfully. Total: ${totalCharges}")
                return totalCharges
            } else {
                log.debug("No service charges to save")
                return 0.00D
            }
            
        } catch (Exception e) {
            log.error("Error saving service charges: ${e.message}", e)
            throw e
        }
    }

    /**
     * Save deductions from session to loan
     */
    def saveDeductions(Loan loanInstance) {
        log.info("Saving deductions for loan: ${loanInstance?.accountNo}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            def deductions = session["deductions"]
            
            if (deductions) {
                def totalDeductions = 0.00D
                
                for (deduction in deductions) {
                    loanInstance.addToLoanDeductions(deduction)
                    totalDeductions += deduction.amount ?: 0.00D
                    
                    log.debug("Added deduction: ${deduction.description} - ${deduction.amount}")
                }
                
                // Clear session after processing
                session["deductions"] = null
                
                log.info("Deductions saved successfully. Total: ${totalDeductions}")
                return totalDeductions
            } else {
                log.debug("No deductions to save")
                return 0.00D
            }
            
        } catch (Exception e) {
            log.error("Error saving deductions: ${e.message}", e)
            throw e
        }
    }

    /**
     * Save sweep accounts from session to loan
     */
    def saveSweepAccounts(Loan loanInstance) {
        log.info("Saving sweep accounts for loan: ${loanInstance?.accountNo}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            def sweepAccounts = session["sweepAccounts"]
            
            if (sweepAccounts) {
                def accountCount = 0
                
                for (sweepAccount in sweepAccounts) {
                    loanInstance.addToSweepAccounts(sweepAccount)
                    accountCount++
                    
                    log.debug("Added sweep account: ${sweepAccount.accountNo}")
                }
                
                // Clear session after processing
                session["sweepAccounts"] = null
                
                log.info("Sweep accounts saved successfully. Count: ${accountCount}")
                return accountCount
            } else {
                log.debug("No sweep accounts to save")
                return 0
            }
            
        } catch (Exception e) {
            log.error("Error saving sweep accounts: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate total deductions for loan
     */
    def calculateTotalDeductions(Loan loanInstance) {
        log.debug("Calculating total deductions for loan: ${loanInstance?.accountNo}")
        
        def totalDeductions = 0.00D
        
        if (loanInstance?.loanDeductions) {
            for (loanDeduction in loanInstance.loanDeductions) {
                totalDeductions += loanDeduction?.amount?.round(2) ?: 0.00D
            }
        }
        
        log.debug("Total deductions calculated: ${totalDeductions}")
        return totalDeductions.round(2)
    }

    /**
     * Calculate total EIR deductions for loan
     */
    def calculateTotalEIRDeductions(Loan loanInstance) {
        log.debug("Calculating total EIR deductions for loan: ${loanInstance?.accountNo}")
        
        def totalDeductionsEir = 0.00D
        
        if (loanInstance?.loanDeductions) {
            for (loanDeduction in loanInstance.loanDeductions) {
                if (loanDeduction?.scheme?.hasEirMode == true) {
                    totalDeductionsEir += loanDeduction?.amount?.round(2) ?: 0.00D
                }
            }
        }
        
        log.debug("Total EIR deductions calculated: ${totalDeductionsEir}")
        return totalDeductionsEir.round(2)
    }

    /**
     * Calculate total service charges for loan
     */
    def calculateTotalServiceCharges(Loan loanInstance) {
        log.debug("Calculating total service charges for loan: ${loanInstance?.accountNo}")
        
        def totalServiceCharges = 0.00D
        
        if (loanInstance?.serviceCharges) {
            for (serviceCharge in loanInstance.serviceCharges) {
                totalServiceCharges += serviceCharge?.amount?.round(2) ?: 0.00D
            }
        }
        
        log.debug("Total service charges calculated: ${totalServiceCharges}")
        return totalServiceCharges.round(2)
    }

    /**
     * Add service charge to loan
     */
    def addServiceCharge(Loan loanInstance, String description, Double amount, String chargeType = null) {
        log.info("Adding service charge to loan: ${loanInstance?.accountNo}")
        
        try {
            def serviceCharge = new LoanServiceCharge(
                description: description,
                amount: amount?.round(2),
                chargeType: chargeType,
                dateCreated: new Date()
            )
            
            loanInstance.addToServiceCharges(serviceCharge)
            
            log.info("Service charge added: ${description} - ${amount}")
            return serviceCharge
            
        } catch (Exception e) {
            log.error("Error adding service charge: ${e.message}", e)
            throw e
        }
    }

    /**
     * Add deduction to loan
     */
    def addDeduction(Loan loanInstance, String description, Double amount, def scheme = null) {
        log.info("Adding deduction to loan: ${loanInstance?.accountNo}")
        
        try {
            def deduction = new LoanDeduction(
                description: description,
                amount: amount?.round(2),
                scheme: scheme,
                dateCreated: new Date()
            )
            
            loanInstance.addToLoanDeductions(deduction)
            
            log.info("Deduction added: ${description} - ${amount}")
            return deduction
            
        } catch (Exception e) {
            log.error("Error adding deduction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Remove service charge from loan
     */
    def removeServiceCharge(Loan loanInstance, Long serviceChargeId) {
        log.info("Removing service charge ${serviceChargeId} from loan: ${loanInstance?.accountNo}")
        
        try {
            def serviceCharge = loanInstance.serviceCharges.find { it.id == serviceChargeId }
            
            if (serviceCharge) {
                loanInstance.removeFromServiceCharges(serviceCharge)
                serviceCharge.delete(flush: true)
                
                log.info("Service charge removed successfully")
                return true
            } else {
                log.warn("Service charge not found: ${serviceChargeId}")
                return false
            }
            
        } catch (Exception e) {
            log.error("Error removing service charge: ${e.message}", e)
            throw e
        }
    }

    /**
     * Remove deduction from loan
     */
    def removeDeduction(Loan loanInstance, Long deductionId) {
        log.info("Removing deduction ${deductionId} from loan: ${loanInstance?.accountNo}")
        
        try {
            def deduction = loanInstance.loanDeductions.find { it.id == deductionId }
            
            if (deduction) {
                loanInstance.removeFromLoanDeductions(deduction)
                deduction.delete(flush: true)
                
                log.info("Deduction removed successfully")
                return true
            } else {
                log.warn("Deduction not found: ${deductionId}")
                return false
            }
            
        } catch (Exception e) {
            log.error("Error removing deduction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Update service charge amount
     */
    def updateServiceChargeAmount(Loan loanInstance, Long serviceChargeId, Double newAmount) {
        log.info("Updating service charge ${serviceChargeId} amount to ${newAmount}")
        
        try {
            def serviceCharge = loanInstance.serviceCharges.find { it.id == serviceChargeId }
            
            if (serviceCharge) {
                def oldAmount = serviceCharge.amount
                serviceCharge.amount = newAmount?.round(2)
                serviceCharge.save(flush: true)
                
                log.info("Service charge amount updated from ${oldAmount} to ${newAmount}")
                return serviceCharge
            } else {
                log.warn("Service charge not found: ${serviceChargeId}")
                return null
            }
            
        } catch (Exception e) {
            log.error("Error updating service charge amount: ${e.message}", e)
            throw e
        }
    }

    /**
     * Update deduction amount
     */
    def updateDeductionAmount(Loan loanInstance, Long deductionId, Double newAmount) {
        log.info("Updating deduction ${deductionId} amount to ${newAmount}")
        
        try {
            def deduction = loanInstance.loanDeductions.find { it.id == deductionId }
            
            if (deduction) {
                def oldAmount = deduction.amount
                deduction.amount = newAmount?.round(2)
                deduction.save(flush: true)
                
                log.info("Deduction amount updated from ${oldAmount} to ${newAmount}")
                return deduction
            } else {
                log.warn("Deduction not found: ${deductionId}")
                return null
            }
            
        } catch (Exception e) {
            log.error("Error updating deduction amount: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate net proceeds after deductions
     */
    def calculateNetProceeds(Loan loanInstance) {
        log.debug("Calculating net proceeds for loan: ${loanInstance?.accountNo}")
        
        def grantedAmount = loanInstance?.grantedAmount ?: 0.00D
        def totalDeductions = calculateTotalDeductions(loanInstance)
        def totalServiceCharges = calculateTotalServiceCharges(loanInstance)
        
        def netProceeds = grantedAmount - totalDeductions - totalServiceCharges
        
        log.debug("Net proceeds calculated: ${grantedAmount} - ${totalDeductions} - ${totalServiceCharges} = ${netProceeds}")
        return netProceeds.round(2)
    }

    /**
     * Validate charges and deductions
     */
    def validateChargesAndDeductions(Loan loanInstance) {
        log.debug("Validating charges and deductions for loan: ${loanInstance?.accountNo}")
        
        def errors = []
        
        // Validate service charges
        if (loanInstance?.serviceCharges) {
            for (serviceCharge in loanInstance.serviceCharges) {
                if (!serviceCharge.amount || serviceCharge.amount < 0) {
                    errors.add("Invalid service charge amount: ${serviceCharge.description}")
                }
            }
        }
        
        // Validate deductions
        if (loanInstance?.loanDeductions) {
            for (deduction in loanInstance.loanDeductions) {
                if (!deduction.amount || deduction.amount < 0) {
                    errors.add("Invalid deduction amount: ${deduction.description}")
                }
            }
        }
        
        // Validate net proceeds
        def netProceeds = calculateNetProceeds(loanInstance)
        if (netProceeds < 0) {
            errors.add("Net proceeds cannot be negative. Total deductions exceed granted amount.")
        }
        
        if (errors.isEmpty()) {
            log.debug("Charges and deductions validation passed")
            return [valid: true, errors: []]
        } else {
            log.warn("Charges and deductions validation failed: ${errors}")
            return [valid: false, errors: errors]
        }
    }

    /**
     * Get charges summary for loan
     */
    def getChargesSummary(Loan loanInstance) {
        log.debug("Getting charges summary for loan: ${loanInstance?.accountNo}")
        
        def summary = [
            grantedAmount: loanInstance?.grantedAmount ?: 0.00D,
            totalServiceCharges: calculateTotalServiceCharges(loanInstance),
            totalDeductions: calculateTotalDeductions(loanInstance),
            totalEIRDeductions: calculateTotalEIRDeductions(loanInstance),
            netProceeds: calculateNetProceeds(loanInstance),
            serviceChargeCount: loanInstance?.serviceCharges?.size() ?: 0,
            deductionCount: loanInstance?.loanDeductions?.size() ?: 0
        ]
        
        log.debug("Charges summary: ${summary}")
        return summary
    }
}
