package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import java.util.GregorianCalendar

import org.icbs.loans.Loan
import org.icbs.loans.LoanEIRSchedule
import org.icbs.loans.LoanUidScPointer
import org.icbs.admin.Institution
import org.icbs.lov.ConfigItemStatus

/**
 * REFACTORED: LoanInterestCalculationService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan interest calculations with modern patterns
 * 
 * This service manages:
 * - Interest rate calculations
 * - Effective Interest Rate (EIR) calculations
 * - IRR (Internal Rate of Return) calculations
 * - UID (Unearned Interest Discount) calculations
 * - Advanced interest computations
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanInterestCalculationService {
    
    /**
     * Calculate effective interest rate for loan
     */
    def calculateEffectiveInterestRate(Loan loanInstance, List cashFlows, Integer numInstallments) {
        log.info("Calculating effective interest rate for loan: ${loanInstance?.accountNo}")
        
        try {
            def irr = customIrr(cashFlows as double[], 0)
            def installmentCalculation = loanInstance?.interestIncomeScheme?.installmentCalcType?.id
            def frequency = loanInstance?.frequency?.id
            
            if ((installmentCalculation == 3 || installmentCalculation == 7) && frequency == 3) {
                // Annuity with weekly amortization
                calculateWeeklyEIR(loanInstance, irr, numInstallments)
            } else if ((installmentCalculation == 3 || installmentCalculation == 7) && 
                      (frequency == 5 || frequency == 14)) {
                // Semi-monthly installment
                loanInstance.effectiveInterestRate = ((((1 + irr)**24) - 1) * 100D).round(5)
                loanInstance.monthlyInterestRate = (irr * 100D).round(5)
            } else {
                // Standard monthly calculation
                loanInstance.effectiveInterestRate = ((((1 + irr)**12) - 1) * 100D).round(5)
                loanInstance.monthlyInterestRate = (irr * 100D).round(5)
            }
            
            // Handle manual installment EIR calculation
            if (installmentCalculation == 6) {
                calculateManualInstallmentEIR(loanInstance, irr, frequency)
            }
            
            // Force rounding
            loanInstance.effectiveInterestRate = loanInstance.effectiveInterestRate.round(5)
            loanInstance.monthlyInterestRate = loanInstance.monthlyInterestRate.round(5)
            
            log.info("EIR calculated: ${loanInstance.effectiveInterestRate}%, MIR: ${loanInstance.monthlyInterestRate}%")
            return [
                effectiveInterestRate: loanInstance.effectiveInterestRate,
                monthlyInterestRate: loanInstance.monthlyInterestRate,
                irr: irr
            ]
            
        } catch (Exception e) {
            log.error("Error calculating effective interest rate: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate UID schedule for loan
     */
    def generateUIDSchedule(Loan loanInstance) {
        log.info("Generating UID schedule for loan: ${loanInstance?.accountNo}")
        
        try {
            double advInterest = 0
            
            def advInterestType = loanInstance?.interestIncomeScheme?.advInterestType?.id
            def installmentCalculation = loanInstance?.interestIncomeScheme?.installmentCalcType.id
            def amount = loanInstance?.grantedAmount ?: 0
            def term = loanInstance?.maturityDate - loanInstance?.interestStartDate
            def interestRate = (loanInstance?.interestRate ?: 0) * 0.01
            def divisor = loanInstance?.interestIncomeScheme?.divisor ?: 0
            
            def advInterestPeriods = loanInstance?.advInterestPeriods ?: 0
            def advInterestDays = loanInstance?.advInterestDays ?: 0
            
            if (advInterestType != 1) {
                // Compute advanced interest
                if (advInterestType == 2) {  // full advanced interest
                    if (installmentCalculation == 1) {  // single payment
                        advInterest = amount * interestRate * (term / divisor)
                    } else if (installmentCalculation == 5) {  // flat
                        advInterest = amount * interestRate
                    }
                } else if (advInterestType == 3) {  // partial advanced interest
                    if (installmentCalculation == 1) {  // single payment
                        advInterest = amount * interestRate * (advInterestDays / divisor)
                    } else if (installmentCalculation == 2 || installmentCalculation == 5) {  // fixed principal or flat
                        def advInterestStart = loanInstance.loanInstallments.find{it.sequenceNo == 1}.installmentDate
                        def advInterestEnd = loanInstance.loanInstallments.find{it.sequenceNo == advInterestPeriods}.installmentDate
                        advInterestDays = advInterestEnd - advInterestStart
                        
                        advInterest = amount * (interestRate / divisor) * advInterestDays
                    }
                }
                
                // Check if EIR implementation is enabled
                def isEIRImplement = Institution.findByParamCode("GEN.10252")?.paramValue
                
                if (isEIRImplement?.toString()?.toLowerCase() == 'true') {
                    log.info("Using EIR-based UID computation")
                    generateUIDandScScheduleIRR(advInterest, loanInstance)
                } else {
                    log.info("Using standard UID computation")
                    generateStandardUIDSchedule(advInterest, loanInstance, installmentCalculation, 
                                              advInterestDays, term)
                }
            }
            
            loanInstance.advInterest = advInterest
            
            log.info("UID schedule generated successfully. Advanced interest: ${advInterest}")
            return advInterest
            
        } catch (Exception e) {
            log.error("Error generating UID schedule: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate IRR using Newton-Raphson method
     */
    def customIrr(double[] values, double guess) {
        log.debug("Calculating IRR with guess: ${guess}")
        
        final int maxIterationCount = 2000
        final double absoluteAccuracy = 1E-7
        
        double x0 = guess
        double x1
        
        int i = 0
        while (i < maxIterationCount) {
            // Calculate NPV and its derivative
            final double factor = 1.0 + x0
            int k = 0
            double fValue = values[k]
            double fDerivative = 0
            
            for (double denominator = factor; ++k < values.length; ) {
                final double value = values[k]
                fValue += value / denominator
                denominator *= factor
                fDerivative -= k * value / denominator
            }
            
            // Newton-Raphson iteration
            x1 = x0 - fValue / fDerivative
            
            if (Math.abs(x1 - x0) <= absoluteAccuracy) {
                log.debug("IRR converged: ${x1}")
                return x1
            }
            
            x0 = x1
            ++i
        }
        
        log.warn("IRR calculation did not converge, returning guess: ${guess}")
        return guess
    }

    /**
     * Calculate interest to date for loan
     */
    def calculateInterestToDate(Loan loanInstance) {
        log.debug("Calculating interest to date for loan: ${loanInstance?.accountNo}")
        
        try {
            def currentDate = new Date()
            def interestStartDate = loanInstance?.interestStartDate ?: loanInstance?.openingDate
            def balance = loanInstance?.balanceAmount ?: 0
            def interestRate = (loanInstance?.interestRate ?: 0) * 0.01
            def divisor = loanInstance?.interestIncomeScheme?.divisor ?: 365
            
            if (currentDate > interestStartDate && balance > 0) {
                def daysDiff = currentDate - interestStartDate
                def interestToDate = balance * interestRate * (daysDiff / divisor)
                
                log.debug("Interest to date calculated: ${interestToDate}")
                return interestToDate.round(2)
            }
            
            return 0.00D
            
        } catch (Exception e) {
            log.error("Error calculating interest to date: ${e.message}", e)
            return 0.00D
        }
    }

    /**
     * Calculate compounding rate based on frequency
     */
    def calculateCompoundingRate(Loan loanInstance, Double contractualRate, Double interestRate) {
        log.debug("Calculating compounding rate for frequency: ${loanInstance?.frequency?.id}")
        
        def frequency = loanInstance?.frequency?.id
        def compoundingRate
        
        switch (frequency) {
            case 1:
            case 2:  // daily
                compoundingRate = contractualRate / 30
                break
            case 3:  // weekly
                compoundingRate = (contractualRate / 30) * 7
                break
            case 4:  // bi-weekly
                compoundingRate = (contractualRate / 30) * 14
                break
            case 5:
            case 14:  // semi-monthly
                compoundingRate = (contractualRate / 30) * 15
                break
            case 6:
            case 7:  // monthly
                compoundingRate = contractualRate
                break
            case 8:  // bi-monthly
                compoundingRate = interestRate / 6
                break
            case 9:  // quarterly
                compoundingRate = interestRate / 4
                break
            case 10:  // semi-annually
                compoundingRate = interestRate / 2
                break
            case 11:  // annually
                compoundingRate = interestRate
                break
            default:
                compoundingRate = contractualRate
        }
        
        log.debug("Compounding rate calculated: ${compoundingRate}")
        return compoundingRate
    }

    // Private helper methods
    
    /**
     * Calculate weekly EIR
     */
    private def calculateWeeklyEIR(Loan loanInstance, Double irr, Integer numInstallments) {
        def period = 52
        def factor = 0
        
        if (numInstallments == 13) {
            factor = 13.div(3)
        } else if (numInstallments == 18) {
            factor = 18.div(4)
        } else if (numInstallments == 22) {
            factor = 22.div(5)
        } else if (numInstallments == 26) {
            factor = 26.div(6)
        } else if (numInstallments == 31) {
            factor = 31.div(7)
        } else if (numInstallments == 36) {
            factor = 36.div(8)
        } else if (numInstallments == 40) {
            factor = 40.div(9)
        } else if (numInstallments == 44) {
            factor = 44.div(10)
        } else if (numInstallments == 48) {
            factor = 48.div(11)
        } else {
            factor = 52.div(12)
        }
        
        loanInstance.effectiveInterestRate = ((((1 + irr)**period) - 1) * 100D).round(5)
        loanInstance.monthlyInterestRate = ((((1 + irr)**factor) - 1) * 100D).round(5)
    }

    /**
     * Calculate manual installment EIR
     */
    private def calculateManualInstallmentEIR(Loan loanInstance, Double irr, Integer frequency) {
        if (frequency == 3) {
            // Weekly
            calculateWeeklyEIR(loanInstance, irr, 0)
        } else if (frequency == 5) {
            // Semi-monthly
            loanInstance.effectiveInterestRate = ((((1 + irr)**24) - 1) * 100D).round(5)
            loanInstance.monthlyInterestRate = (irr * 100D).round(5)
        } else if (frequency == 7) {
            // Monthly
            loanInstance.effectiveInterestRate = ((((1 + irr)**12) - 1) * 100D).round(5)
            loanInstance.monthlyInterestRate = (irr * 100D).round(5)
        } else if (frequency == 9) {
            // Quarterly
            loanInstance.effectiveInterestRate = ((((1 + irr)**4) - 1) * 100D).round(5)
            loanInstance.monthlyInterestRate = (irr * 100D).round(5)
        } else if (frequency == 10) {
            // Semi-annual
            loanInstance.effectiveInterestRate = ((((1 + irr)**2) - 1) * 100D).round(5)
            loanInstance.monthlyInterestRate = (irr * 100D).round(5)
        } else {
            // Default to monthly
            loanInstance.effectiveInterestRate = ((((1 + irr)**12) - 1) * 100D).round(5)
            loanInstance.monthlyInterestRate = (irr * 100D).round(5)
        }
    }

    /**
     * Generate standard UID schedule
     */
    private def generateStandardUIDSchedule(Double advInterest, Loan loanInstance, 
                                          Integer installmentCalculation, Integer advInterestDays, Integer term) {
        if (installmentCalculation == 1) {
            generateSinglePaymentUIDSchedule(advInterest, loanInstance, advInterestDays, term)
        } else if (installmentCalculation == 2 || installmentCalculation == 5) {
            generateInstallmentUIDSchedule(advInterest, loanInstance, term)
        }
    }

    /**
     * Generate single payment UID schedule
     */
    private def generateSinglePaymentUIDSchedule(Double advInterest, Loan loanInstance, 
                                               Integer advInterestDays, Integer term) {
        def advInterestPerDay = advInterest / advInterestDays
        def calendar = new GregorianCalendar()
        
        def transferEndDate = loanInstance?.interestStartDate + advInterestDays
        calendar.setTime(transferEndDate)
        def transferEndMonth = calendar.get(Calendar.MONTH)
        
        def prevTransferDate = loanInstance?.interestStartDate
        calendar.setTime(prevTransferDate)
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        def endOfMonth = calendar.getTime()
        
        def transferDate = endOfMonth
        if (transferEndDate < transferDate) {
            transferDate = transferEndDate
        }
        
        while (transferDate.clearTime() <= transferEndDate.clearTime()) {
            def uidAmount = advInterestPerDay * (transferDate - prevTransferDate)
            
            def eirSchedule = new LoanEIRSchedule(transferDate: transferDate, uidAmount: uidAmount.round(2))
            loanInstance.addToLoanEIRSchedules(eirSchedule)
            
            prevTransferDate = transferDate
            
            calendar.add(Calendar.MONTH, 1)
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
            endOfMonth = calendar.getTime()
            
            def currentMonth = calendar.get(Calendar.MONTH)
            
            transferDate = endOfMonth
            if (currentMonth == transferEndMonth && transferEndDate < transferDate) {
                transferDate = transferEndDate
            }
        }
    }

    /**
     * Generate installment UID schedule
     */
    private def generateInstallmentUIDSchedule(Double advInterest, Loan loanInstance, Integer term) {
        def freq = loanInstance?.product?.loanUidTransferFreq?.id
        def count = 0
        
        if (freq == 1) {
            count = term
        } else if (freq == 2 || freq == 3) {
            count = (term / 7D).round(0)
        } else if (freq == 4) {
            count = (term / 30D).round(0)
        } else if (freq == 5) {
            count = (term / 90D).round(0)
        } else if (freq == 6) {
            count = (term / 180D).round(0)
        } else if (freq == 7) {
            count = (term / 365D).round(0)
        }
        
        def uidAmount = advInterest / count
        def prevTransferDate = loanInstance?.interestStartDate
        
        for (int i = 0; i < count; i++) {
            def calendar = new GregorianCalendar()
            calendar.setTime(prevTransferDate)
            
            // Calculate transfer date based on frequency
            calculateTransferDate(calendar, freq)
            
            def transferDate = calendar.getTime()
            
            def eirSchedule = new LoanEIRSchedule(transferDate: transferDate, uidAmount: uidAmount.round(2))
            loanInstance.addToLoanEIRSchedules(eirSchedule)
            
            prevTransferDate = transferDate
        }
    }

    /**
     * Calculate transfer date based on frequency
     */
    private def calculateTransferDate(GregorianCalendar calendar, Integer freq) {
        switch (freq) {
            case 1:  // daily
                calendar.add(Calendar.DAY_OF_MONTH, 1)
                break
            case 2:  // weekly (last day)
                calendar.add(Calendar.DAY_OF_MONTH, 7)
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY)
                break
            case 3:  // weekly (first day)
                calendar.add(Calendar.DAY_OF_MONTH, 7)
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
                break
            case 4:  // monthly
                calendar.add(Calendar.MONTH, 1)
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                break
            case 5:  // quarterly
                calendar.add(Calendar.MONTH, 3)
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                break
            case 6:  // semi-annually
                calendar.add(Calendar.MONTH, 6)
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                break
            case 7:  // annually
                calendar.add(Calendar.YEAR, 1)
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                break
        }
    }

    /**
     * Generate UID and SC schedule using IRR
     */
    private def generateUIDandScScheduleIRR(Double totalInterest, Loan loanInstance) {
        log.info("Generating UID and SC schedule using IRR method")
        
        // Compute SC deductions
        def loanDeduction = loanInstance.loanDeductions.findAll()
        Double scAmt = 0.00D
        
        for (l in loanDeduction) {
            def loanServiceChargePointer = LoanUidScPointer.findByLoanDeductionSchemeAndStatus(
                l.scheme, ConfigItemStatus.get(2))
            if (loanServiceChargePointer) {
                scAmt += l.amount
            }
        }
        
        def theUIDdate = loanInstance?.interestStartDate
        def term = loanInstance?.maturityDate - loanInstance?.interestStartDate
        
        // Compute IRR for interest and SC
        def carryingAmt = loanInstance?.grantedAmount - totalInterest
        def carryingAmtSc = loanInstance?.grantedAmount - scAmt
        
        def maxSc = term > 365D ? 12 : term.div(30).toInteger()
        
        def irr = Math.pow((1 + totalInterest / (loanInstance?.grantedAmount - totalInterest)), (1/(term/30))) - 1
        def irrSc = Math.pow((1 + scAmt / (loanInstance?.grantedAmount - scAmt)), (1/(term/30))) - 1
        
        irr = irr.round(4)
        irrSc = irrSc.round(4)
        
        def calendar = new GregorianCalendar()
        calendar.setTime(theUIDdate)
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        def endOfMonth = calendar.getTime()
        
        def totLnDisc = 0.00D
        def totScDisc = 0.00D
        
        for (int i = 1; i <= maxSc; i++) {
            def loanDiscount = carryingAmt * irr
            def loanDiscountSc = carryingAmtSc * irrSc
            
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
            endOfMonth = calendar.getTime()
            
            if (i <= maxSc - 1) {
                totLnDisc += loanDiscount.round(2)
                totScDisc += loanDiscountSc.round(2)
            }
            
            // Adjust for last to match total
            if (i == maxSc) {
                loanDiscount = totalInterest - totLnDisc
                loanDiscountSc = scAmt - totScDisc
            }
            
            if (endOfMonth < loanInstance?.maturityDate) {
                def eirSchedule = new LoanEIRSchedule(
                    transferDate: endOfMonth, 
                    uidAmount: loanDiscount.round(2),
                    serviceChargeAmount: loanDiscountSc.round(2)
                )
                loanInstance.addToLoanEIRSchedules(eirSchedule)
            }
            
            carryingAmt += loanDiscount.round(2)
            carryingAmtSc += loanDiscountSc.round(2)
            
            theUIDdate += 30
            calendar.add(Calendar.MONTH, 1)
        }
        
        log.info("UID and SC schedule generated using IRR method")
    }
}
