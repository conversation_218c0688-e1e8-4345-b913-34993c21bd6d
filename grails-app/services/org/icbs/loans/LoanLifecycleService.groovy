package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Institution
import org.icbs.admin.TxnTemplate
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.loans.LoanReClassHist
import org.icbs.loans.LoanBranchTransfer
import org.icbs.lov.LoanPerformanceId
import org.icbs.lov.ConfigItemStatus
import org.icbs.tellering.TxnFile
import org.icbs.gl.CfgAcctGlTemplate

/**
 * REFACTORED: LoanLifecycleService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan lifecycle operations with modern patterns
 * 
 * This service manages:
 * - Loan creation and saving
 * - Loan renewal operations
 * - Loan amendments and updates
 * - Branch transfers
 * - GL classification updates
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanLifecycleService {
    
    // Service Dependencies
    def loanAccountManagementService
    def loanScheduleService
    def loanChargeService
    def glTransactionService
    
    /**
     * Save new loan with complete setup
     */
    def saveLoan(Loan loanInstance, def installmentAmount) {
        log.info("Saving new loan: ${loanInstance?.accountNo}")
        
        try {
            // Generate account number if not exists
            if (!loanInstance.accountNo) {
                loanAccountManagementService.generateAccountNo(loanInstance) 
            }
            
            // Set interest start date
            if (!loanInstance?.interestStartDate) {
                loanInstance.interestStartDate = loanInstance?.openingDate
            }
            
            // Set maturity date
            loanInstance.maturityDate = loanAccountManagementService.getMaturityDate(loanInstance)
            loanInstance.save(flush: true)

            // Save related components
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)             
            loanScheduleService.generateInstallmentSchedule(loanInstance, installmentAmount)
            loanScheduleService.generateUIDSchedule(loanInstance)

            loanInstance.save(flush: true)
            
            // Set promissory note number if enabled
            loanAccountManagementService.setPromissoryNoteNumber(loanInstance)
            
            // Create loan loss provision
            loanAccountManagementService.createLoanLossProvision(loanInstance)

            log.info("Loan saved successfully: ${loanInstance.accountNo}")
            return loanInstance
            
        } catch (Exception e) {
            log.error("Error saving loan: ${e.message}", e)
            throw e
        }
    }
    
    /**
     * Renew existing loan
     */
    def renewLoan(Loan loanInstance, def installmentAmount) {
        log.info("Renewing loan: ${loanInstance?.accountNo}")
        
        try {
            // Set interest start date
            if (!loanInstance?.interestStartDate) {
                loanInstance.interestStartDate = loanInstance?.openingDate
            }
            
            // Update maturity date
            loanInstance.maturityDate = loanAccountManagementService.getMaturityDate(loanInstance)
            loanInstance.save(flush: true)
            
            // Regenerate schedules and charges
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)
            loanScheduleService.generateInstallmentSchedule(loanInstance, installmentAmount)
            loanScheduleService.generateUIDSchedule(loanInstance)
            
            // Generate new account number for renewal
            loanAccountManagementService.generateAccountNo(loanInstance) 
            
            loanInstance.save(flush: true)
            
            // Set promissory note number if enabled
            loanAccountManagementService.setPromissoryNoteNumber(loanInstance)
            
            log.info("Loan renewed successfully: ${loanInstance.accountNo}")
            return loanInstance
            
        } catch (Exception e) {
            log.error("Error renewing loan: ${e.message}", e)
            throw e
        }
    }
    
    /**
     * Save loan amendment with reclassification tracking
     */
    def saveLoanAmendment(Loan loanInstance, UserMaster user) {
        log.info("Saving loan amendment for: ${loanInstance.accountNo}")
        
        try {
            def oldClass = LoanPerformanceId.findByDescription(loanInstance.getPersistentValue('loanPerformanceId'))
            def newClass = loanInstance.loanPerformanceId
               
            if (oldClass != newClass) {
                // Create reclassification transaction
                createReclassificationTransaction(loanInstance, oldClass, newClass, user)
            }
            
            loanInstance.save(flush: true)
            
            log.info("Loan amendment saved successfully")
            return loanInstance
            
        } catch (Exception e) {
            log.error("Error saving loan amendment: ${e.message}", e)
            throw e
        }
    }

    /**
     * Update loan interest rate
     */
    def updateInterestRate(Loan loanInstance, def interestRate) {
        log.info("Updating interest rate for loan: ${loanInstance.accountNo} to ${interestRate}%")
        
        try {
            loanInstance.interestRate = interestRate
            loanInstance.save(flush: true)

            // Regenerate schedules with new rate
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)      
            loanScheduleService.generateInstallmentSchedule(loanInstance, null)
            loanScheduleService.generateUIDSchedule(loanInstance)
            
            loanInstance.save(flush: true)
            
            log.info("Interest rate updated successfully")
            return loanInstance
            
        } catch (Exception e) {
            log.error("Error updating interest rate: ${e.message}", e)
            throw e
        }
    }

    /**
     * Update interest accrual setting
     */
    def updateInterestAccrual(Loan loanInstance, boolean hasInterestAccrual) {
        log.info("Updating interest accrual for loan: ${loanInstance.accountNo} to ${hasInterestAccrual}")
        
        try {
            loanInstance.hasInterestAccrual = hasInterestAccrual
            loanInstance.save(flush: true)

            // Regenerate schedules
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)      
            loanScheduleService.saveInstallments(loanInstance)
            loanScheduleService.saveEIRSchedules(loanInstance)
            
            loanInstance.save(flush: true)
            
            log.info("Interest accrual updated successfully")
            return loanInstance
            
        } catch (Exception e) {
            log.error("Error updating interest accrual: ${e.message}", e)
            throw e
        }
    }

    /**
     * Transfer loan to different branch
     */
    def updateBranch(Loan loanInstance, Branch branch, String particulars, String reference, UserMaster user) {
        log.info("Transferring loan ${loanInstance.accountNo} from ${loanInstance.branch.name} to ${branch.name}")
        
        try {
            def oldBranch = loanInstance.branch
            loanInstance.branch = branch
            loanInstance.save(flush: true)
            
            // Create transfer transactions
            def transferResult = createBranchTransferTransactions(loanInstance, oldBranch, branch, particulars, reference, user)
            
            // Regenerate schedules for new branch
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)      
            loanScheduleService.saveInstallments(loanInstance)
            loanScheduleService.saveEIRSchedules(loanInstance)
            
            loanInstance.save(flush: true)
            
            // Process GL entries
            glTransactionService.saveLoanTransferBranchEntry(transferResult.transfer)
            
            log.info("Branch transfer completed successfully")
            return transferResult
            
        } catch (Exception e) {
            log.error("Error transferring loan branch: ${e.message}", e)
            throw e
        }
    }

    /**
     * Update GL classification for loan
     */
    def updateGLClassification(Loan loanInstance, CfgAcctGlTemplate glLink, UserMaster user) {
        log.info("Updating GL classification for loan: ${loanInstance.accountNo}")
        
        try {
            loanInstance.prevGLLink = loanInstance.glLink
            loanInstance.glLink = glLink
            loanInstance.save(flush: true)

            // Regenerate schedules
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)      
            loanScheduleService.saveInstallments(loanInstance)
            loanScheduleService.saveEIRSchedules(loanInstance)
            
            loanInstance.save(flush: true)
            
            // Create GL update transaction
            createGLUpdateTransaction(loanInstance, user)
            
            log.info("GL classification updated successfully")
            return loanInstance
            
        } catch (Exception e) {
            log.error("Error updating GL classification: ${e.message}", e)
            throw e
        }
    }

    // Private helper methods
    
    /**
     * Create reclassification transaction
     */
    private def createReclassificationTransaction(Loan loanInstance, def oldClass, def newClass, UserMaster user) {
        def txnTmp = TxnTemplate.get(Institution.findByParamCode('LNS.50075').paramValue.toInteger())
        
        def tf = new TxnFile(
            acctNo: loanInstance.accountNo, 
            branch: loanInstance.branch, 
            currency: loanInstance.product.currency,
            loanAcct: loanInstance, 
            status: ConfigItemStatus.read(2), 
            txnAmt: loanInstance.balanceAmount, 
            txnCode: txnTmp.code,
            txnDate: Branch.get(1).runDate, 
            txnDescription: 'Manual Reclassification of Loan', 
            txnParticulars: 'reclassify loan',
            txnRef: 'Loan Reclass', 
            txnTemplate: txnTmp, 
            txnType: txnTmp.txnType,
            txnTimestamp: new Date().toTimestamp(), 
            user: user
        )
        tf.save(flush: true, failOnError: true)
    
        def ll = new LoanLedger(
            loan: loanInstance, 
            principalDebit: loanInstance.balanceAmount, 
            principalCredit: loanInstance.balanceAmount, 
            principalBalance: loanInstance.balanceAmount, 
            txnFile: tf, 
            txnCode: tf.txnCode, 
            txnDate: Branch.get(1).runDate,
            txnRef: tf.txnRef, 
            txnParticulars: 'Manual Reclassification', 
            txnTemplate: tf.txnTemplate
        )
        ll.save(flush: true, failOnError: true)

        def rc = new LoanReClassHist(
            loanAcct: loanInstance, 
            newClass: newClass.id, 
            oldClass: oldClass.id, 
            reclassDate: Branch.get(1).runDate,
            reclassDesc: 'Manual Reclassification', 
            txnFile: tf
        )
        rc.save(flush: true, failOnError: true)           
        
        glTransactionService.saveTxnBreakdown(tf.id)
        
        return [txnFile: tf, ledger: ll, reclassHistory: rc]
    }

    /**
     * Create branch transfer transactions
     */
    private def createBranchTransferTransactions(Loan loanInstance, Branch oldBranch, Branch newBranch, 
                                               String particulars, String reference, UserMaster user) {
        def tmpDr = TxnTemplate.get(Institution.findByParamCode("LNS.50110").paramValue.toInteger())
        def tmpCr = TxnTemplate.get(Institution.findByParamCode("LNS.50120").paramValue.toInteger())
        
        // Credit transaction (old branch)
        def trCr = new TxnFile(
            acctNo: loanInstance.accountNo, 
            branch: oldBranch, 
            currency: loanInstance.product.currency,
            loanAcct: loanInstance, 
            status: ConfigItemStatus.read(2), 
            txnAmt: loanInstance.balanceAmount, 
            txnCode: tmpCr.code,
            txnDate: Branch.get(1).runDate, 
            txnDescription: 'Loan Branch Transfer Credit', 
            txnParticulars: particulars,
            txnRef: reference, 
            txnTemplate: tmpCr, 
            txnType: tmpCr.txnType,
            txnTimestamp: new Date().toTimestamp(), 
            user: user
        )
        trCr.save(flush: true)    
        
        // Debit transaction (new branch)
        def trDr = new TxnFile(
            acctNo: loanInstance.accountNo, 
            branch: newBranch, 
            currency: loanInstance.product.currency,
            loanAcct: loanInstance, 
            status: ConfigItemStatus.read(2), 
            txnAmt: loanInstance.balanceAmount, 
            txnCode: tmpDr.code,
            txnDate: Branch.get(1).runDate, 
            txnDescription: 'Loan Branch Transfer Debit', 
            txnParticulars: particulars,
            txnRef: reference, 
            txnTemplate: tmpDr, 
            txnType: tmpDr.txnType,
            txnTimestamp: new Date().toTimestamp(), 
            user: user
        )
        trDr.save(flush: true)     

        // Create ledger entries
        def llCr = new LoanLedger(
            loan: loanInstance, 
            txnFile: trCr, 
            txnDate: Branch.get(1).runDate, 
            txnTemplate: tmpCr, 
            principalCredit: loanInstance.balanceAmount, 
            principalDebit: 0.00D, 
            txnRef: trCr.txnRef,
            principalBalance: 0.00D, 
            txnParticulars: particulars
        )
        llCr.save(flush: true, failOnError: true)
        
        def llDr = new LoanLedger(
            loan: loanInstance, 
            txnFile: trDr, 
            txnDate: Branch.get(1).runDate, 
            txnTemplate: tmpDr, 
            principalCredit: 0.00, 
            principalDebit: loanInstance.balanceAmount, 
            txnRef: trDr.txnRef,
            principalBalance: loanInstance.balanceAmount, 
            txnParticulars: particulars
        )
        llDr.save(flush: true, failOnError: true)
        
        // Create transfer record
        def transfer = new LoanBranchTransfer(
            loan: loanInstance, 
            newBranch: newBranch, 
            oldBranch: oldBranch, 
            loanDr: trDr, 
            loanCr: trCr, 
            transferDate: newBranch.runDate, 
            particulars: particulars, 
            reference: reference, 
            user: user, 
            userBranch: user.branch
        )
        transfer.save(flush: true)
        
        return [transfer: transfer, creditTxn: trCr, debitTxn: trDr, creditLedger: llCr, debitLedger: llDr]
    }

    /**
     * Create GL update transaction
     */
    private def createGLUpdateTransaction(Loan loanInstance, UserMaster user) {
        def templateId = Institution.findByParamCode('GEN.10241').paramValue.toInteger()
        def template = TxnTemplate.get(templateId)
        
        def tf = new TxnFile(
            acctNo: loanInstance.accountNo, 
            loanAcct: loanInstance, 
            currency: loanInstance.product.currency,
            user: user, 
            branch: loanInstance.branch, 
            txnAmt: loanInstance.balanceAmount, 
            txnCode: template.code, 
            txnDate: Branch.get(1).runDate,
            txnTimestamp: new Date().toTimestamp(), 
            txnDescription: 'Loan GL Link Update',
            status: ConfigItemStatus.get(2), 
            txnType: template.txnType, 
            txnRef: 'Loan GL Link Update', 
            txnParticulars: 'To update Loan GL Link', 
            txnTemplate: template
        )
        tf.save(flush: true, failOnError: true)
        
        def ll = new LoanLedger(
            loan: loanInstance, 
            txnFile: tf, 
            txnDate: Branch.get(1).runDate, 
            txnTemplate: template, 
            principalCredit: loanInstance.balanceAmount, 
            principalDebit: loanInstance.balanceAmount, 
            txnRef: tf.txnRef,
            principalBalance: loanInstance.balanceAmount
        )
        ll.save(flush: true, failOnError: true)
        
        glTransactionService.saveTxnBreakdown(tf.id)
        
        return [txnFile: tf, ledger: ll]
    }
}
