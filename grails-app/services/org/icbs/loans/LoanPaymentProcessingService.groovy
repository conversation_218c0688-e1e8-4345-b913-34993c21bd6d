package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import javax.servlet.http.HttpSession
import org.springframework.web.context.request.RequestContextHolder

import org.icbs.loans.Loan
import org.icbs.loans.LoanHistory
import org.icbs.loans.LoanSpecial
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanSpecialType

/**
 * REFACTORED: LoanPaymentProcessingService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan payment processing operations with modern patterns
 * 
 * This service manages:
 * - Payment processing and allocation
 * - Write-off operations
 * - ROPA (Return of Performing Asset) operations
 * - Payment calculations and validations
 * - Loan history tracking
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanPaymentProcessingService {
    
    // Service Dependencies
    def loanChargeService
    def loanScheduleService
    
    /**
     * Process loan payment with proper allocation
     */
    def processPayment(Loan loanInstance, Double paymentAmount, String paymentType = 'REGULAR') {
        log.info("Processing payment for loan: ${loanInstance?.accountNo}, amount: ${paymentAmount}")
        
        try {
            validatePayment(loanInstance, paymentAmount)
            
            def paymentAllocation = calculatePaymentAllocation(loanInstance, paymentAmount)
            
            // Apply payment to loan balances
            applyPaymentToBalances(loanInstance, paymentAllocation)
            
            // Update loan status if necessary
            updateLoanStatusAfterPayment(loanInstance)
            
            // Create payment history record
            createPaymentHistory(loanInstance, paymentAmount, paymentType, paymentAllocation)
            
            loanInstance.save(flush: true)
            
            log.info("Payment processed successfully")
            return [
                success: true,
                paymentAmount: paymentAmount,
                allocation: paymentAllocation,
                newBalance: loanInstance.balanceAmount
            ]
            
        } catch (Exception e) {
            log.error("Error processing payment: ${e.message}", e)
            throw e
        }
    }

    /**
     * Write off loan account
     */
    def writeOff(Loan loanInstance) {
        log.info("Writing off loan: ${loanInstance?.accountNo}")
        
        try {
            // Check for collaterals
            if (loanInstance?.loanApplication?.collaterals?.size() > 0) {
                loanInstance.grantedAmount = 0
            } else {
                loanInstance.balanceAmount = 1
            }
            
            // Update status to written off
            loanInstance.status = LoanAcctStatus.get(8)
            
            // Create or update special loan record
            def type = LoanSpecialType.get(2)
            def action = "WRITE_OFF"
            def transferDate = new Date()
            
            if (loanInstance.special) {
                loanInstance.special.type = type
                loanInstance.special.action = action
                loanInstance.special.transferDate = transferDate
            } else {
                def special = new LoanSpecial(type: type, action: action, transferDate: transferDate)
                special.save(flush: true)
                loanInstance.special = special
            }
            
            loanInstance.save(flush: true)
            
            log.info("Loan written off successfully")
            return [success: true, writeOffDate: transferDate]
            
        } catch (Exception e) {
            log.error("Error writing off loan: ${e.message}", e)
            throw e
        }
    }

    /**
     * Return of Performing Asset (ROPA) operation
     */
    def ropa(Loan loanInstance) {
        log.info("Processing ROPA for loan: ${loanInstance?.accountNo}")
        
        try {
            def type = LoanSpecialType.get(3)
            def action = "ROPA"
            def transferDate = new Date()
            
            if (loanInstance.special) {
                loanInstance.special.type = type
                loanInstance.special.action = action
                loanInstance.special.transferDate = transferDate
            } else {
                def special = new LoanSpecial(type: type, action: action, transferDate: transferDate)
                special.save(flush: true)
                loanInstance.special = special
            }
            
            loanInstance.save(flush: true)
            
            // Regenerate schedules and charges
            loanChargeService.saveServiceCharges(loanInstance)
            loanChargeService.saveDeductions(loanInstance)
            loanChargeService.saveSweepAccounts(loanInstance)
            loanScheduleService.saveInstallments(loanInstance)
            loanScheduleService.saveEIRSchedules(loanInstance)
            
            loanInstance.save(flush: true)
            
            log.info("ROPA processed successfully")
            return [success: true, ropaDate: transferDate]
            
        } catch (Exception e) {
            log.error("Error processing ROPA: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate payment allocation across different components
     */
    def calculatePaymentAllocation(Loan loanInstance, Double paymentAmount) {
        log.debug("Calculating payment allocation for amount: ${paymentAmount}")
        
        def allocation = [
            penalties: 0.00D,
            pastDuePenalties: 0.00D,
            pastDueInterest: 0.00D,
            currentInterest: 0.00D,
            serviceCharges: 0.00D,
            principal: 0.00D,
            taxes: 0.00D,
            remaining: paymentAmount
        ]
        
        // Allocate to penalties first
        if (allocation.remaining > 0 && loanInstance.penaltyBalanceAmount > 0) {
            def penaltyPayment = Math.min(allocation.remaining, loanInstance.penaltyBalanceAmount)
            allocation.penalties = penaltyPayment
            allocation.remaining -= penaltyPayment
        }
        
        // Allocate to past due interest
        if (allocation.remaining > 0 && loanInstance.interestBalanceAmount > 0) {
            def interestPayment = Math.min(allocation.remaining, loanInstance.interestBalanceAmount)
            allocation.pastDueInterest = interestPayment
            allocation.remaining -= interestPayment
        }
        
        // Allocate to service charges
        if (allocation.remaining > 0 && loanInstance.serviceChargeBalanceAmount > 0) {
            def serviceChargePayment = Math.min(allocation.remaining, loanInstance.serviceChargeBalanceAmount)
            allocation.serviceCharges = serviceChargePayment
            allocation.remaining -= serviceChargePayment
        }
        
        // Allocate to principal
        if (allocation.remaining > 0 && loanInstance.balanceAmount > 0) {
            def principalPayment = Math.min(allocation.remaining, loanInstance.balanceAmount)
            allocation.principal = principalPayment
            allocation.remaining -= principalPayment
        }
        
        log.debug("Payment allocation calculated: ${allocation}")
        return allocation
    }

    /**
     * Apply payment allocation to loan balances
     */
    def applyPaymentToBalances(Loan loanInstance, Map allocation) {
        log.debug("Applying payment allocation to loan balances")
        
        // Update penalty balance
        if (allocation.penalties > 0) {
            loanInstance.penaltyBalanceAmount = Math.max(0, loanInstance.penaltyBalanceAmount - allocation.penalties)
        }
        
        // Update interest balance
        if (allocation.pastDueInterest > 0) {
            loanInstance.interestBalanceAmount = Math.max(0, loanInstance.interestBalanceAmount - allocation.pastDueInterest)
        }
        
        // Update service charge balance
        if (allocation.serviceCharges > 0) {
            loanInstance.serviceChargeBalanceAmount = Math.max(0, loanInstance.serviceChargeBalanceAmount - allocation.serviceCharges)
        }
        
        // Update principal balance
        if (allocation.principal > 0) {
            loanInstance.balanceAmount = Math.max(0, loanInstance.balanceAmount - allocation.principal)
        }
        
        // Update total disbursement if principal was paid
        if (allocation.principal > 0) {
            loanInstance.totalDisbursementAmount = loanInstance.grantedAmount - loanInstance.balanceAmount
        }
        
        log.debug("Payment allocation applied successfully")
    }

    /**
     * Update loan status after payment
     */
    def updateLoanStatusAfterPayment(Loan loanInstance) {
        log.debug("Updating loan status after payment")
        
        def totalBalance = loanInstance.balanceAmount + 
                          loanInstance.interestBalanceAmount + 
                          loanInstance.penaltyBalanceAmount + 
                          loanInstance.serviceChargeBalanceAmount
        
        if (totalBalance <= 0.01) {  // Allow for rounding differences
            // Loan is fully paid
            loanInstance.status = LoanAcctStatus.get(6)  // Closed/Paid
            loanInstance.statusChangedDate = new Date()
            log.info("Loan status updated to PAID")
        } else if (loanInstance.status.id == 6 && totalBalance > 0.01) {
            // Reopen loan if it was closed but now has balance
            loanInstance.status = LoanAcctStatus.get(2)  // Active
            loanInstance.statusChangedDate = new Date()
            log.info("Loan status updated to ACTIVE")
        }
    }

    /**
     * Create payment history record
     */
    def createPaymentHistory(Loan loanInstance, Double paymentAmount, String paymentType, Map allocation) {
        log.debug("Creating payment history record")
        
        try {
            def paymentHistory = new LoanHistory(
                loan: loanInstance,
                activity: "PAYMENT_${paymentType}",
                paymentAmount: paymentAmount,
                principalPayment: allocation.principal,
                interestPayment: allocation.pastDueInterest,
                penaltyPayment: allocation.penalties,
                serviceChargePayment: allocation.serviceCharges,
                dateModified: new Date(),
                balanceAfterPayment: loanInstance.balanceAmount,
                remarks: "Payment processed via ${paymentType}"
            )
            
            paymentHistory.save(flush: true)
            
            log.debug("Payment history record created successfully")
            return paymentHistory
            
        } catch (Exception e) {
            log.error("Error creating payment history: ${e.message}", e)
            // Don't throw exception as this is not critical for payment processing
            return null
        }
    }

    /**
     * Validate payment before processing
     */
    def validatePayment(Loan loanInstance, Double paymentAmount) {
        log.debug("Validating payment")
        
        if (!loanInstance) {
            throw new IllegalArgumentException("Loan instance is required")
        }
        
        if (!paymentAmount || paymentAmount <= 0) {
            throw new IllegalArgumentException("Payment amount must be greater than zero")
        }
        
        if (loanInstance.status.id == 6) {
            throw new IllegalStateException("Cannot process payment for closed loan")
        }
        
        if (loanInstance.status.id == 8) {
            throw new IllegalStateException("Cannot process payment for written-off loan")
        }
        
        log.debug("Payment validation passed")
    }

    /**
     * Calculate total outstanding balance
     */
    def calculateTotalOutstanding(Loan loanInstance) {
        log.debug("Calculating total outstanding balance")
        
        def totalOutstanding = (loanInstance.balanceAmount ?: 0) +
                              (loanInstance.interestBalanceAmount ?: 0) +
                              (loanInstance.penaltyBalanceAmount ?: 0) +
                              (loanInstance.serviceChargeBalanceAmount ?: 0) +
                              (loanInstance.taxBalanceAmount ?: 0)
        
        log.debug("Total outstanding calculated: ${totalOutstanding}")
        return totalOutstanding.round(2)
    }

    /**
     * Calculate minimum payment due
     */
    def calculateMinimumPaymentDue(Loan loanInstance) {
        log.debug("Calculating minimum payment due")
        
        def currentDate = new Date()
        def minimumDue = 0.00D
        
        // Find overdue installments
        def overdueInstallments = loanInstance.loanInstallments.findAll { 
            it.installmentDate <= currentDate && it.status.id == 2  // Unpaid
        }
        
        for (installment in overdueInstallments) {
            minimumDue += (installment.totalInstallmentAmount - installment.totalInstallmentPaid)
        }
        
        // Add any penalty and interest balances
        minimumDue += (loanInstance.penaltyBalanceAmount ?: 0)
        minimumDue += (loanInstance.interestBalanceAmount ?: 0)
        
        log.debug("Minimum payment due calculated: ${minimumDue}")
        return minimumDue.round(2)
    }

    /**
     * Commit loan history entry from session
     */
    def commitLoanHistoryEntry(String activity) {
        log.info("Committing loan history entry for activity: ${activity}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            
            if (session["loanHistory"]) {
                def loanHistory = session["loanHistory"].get(0)
                
                loanHistory.serviceCharges = session["newServiceCharges"]
                session["newServiceCharges"] = null
                loanHistory.loanDeductions = session["newDeductions"]
                session["newDeductions"] = null
                loanHistory.loanInstallments = session["newInstallments"]
                session["newInstallments"] = null
                loanHistory.loanEIRSchedules = session["newEIRSchedules"]
                session["newEIRSchedules"] = null
                session["newSweepAccounts"] = null
                
                loanHistory.activity = activity
                loanHistory.dateModified = new Date()
                
                loanHistory.save(flush: true)
                session["loanHistory"] = null
                
                log.info("Loan history entry committed successfully")
                return loanHistory
            } else {
                log.debug("No loan history in session to commit")
                return null
            }
            
        } catch (Exception e) {
            log.error("Error committing loan history entry: ${e.message}", e)
            throw e
        }
    }

    /**
     * Get payment summary for loan
     */
    def getPaymentSummary(Loan loanInstance) {
        log.debug("Getting payment summary for loan: ${loanInstance?.accountNo}")
        
        def summary = [
            accountNo: loanInstance.accountNo,
            grantedAmount: loanInstance.grantedAmount,
            currentBalance: loanInstance.balanceAmount,
            interestBalance: loanInstance.interestBalanceAmount,
            penaltyBalance: loanInstance.penaltyBalanceAmount,
            serviceChargeBalance: loanInstance.serviceChargeBalanceAmount,
            taxBalance: loanInstance.taxBalanceAmount,
            totalOutstanding: calculateTotalOutstanding(loanInstance),
            minimumPaymentDue: calculateMinimumPaymentDue(loanInstance),
            status: loanInstance.status.description,
            lastPaymentDate: getLastPaymentDate(loanInstance),
            nextDueDate: getNextDueDate(loanInstance)
        ]
        
        log.debug("Payment summary: ${summary}")
        return summary
    }

    /**
     * Get last payment date
     */
    private def getLastPaymentDate(Loan loanInstance) {
        def lastPayment = LoanHistory.findByLoanAndActivityLike(loanInstance, "PAYMENT_%", [sort: "dateModified", order: "desc"])
        return lastPayment?.dateModified
    }

    /**
     * Get next due date
     */
    private def getNextDueDate(Loan loanInstance) {
        def currentDate = new Date()
        def nextInstallment = loanInstance.loanInstallments.find { 
            it.installmentDate > currentDate && it.status.id == 2  // Unpaid
        }
        return nextInstallment?.installmentDate
    }
}
