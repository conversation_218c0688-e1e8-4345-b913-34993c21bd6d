package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanHistory
import org.icbs.loans.LoanLedger
import org.icbs.admin.Branch
import org.icbs.cif.Customer
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanPerformanceClassification

/**
 * REFACTORED: LoanReportingService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan reporting and utility operations with modern patterns
 * 
 * This service manages:
 * - Loan portfolio reporting
 * - Performance analytics
 * - Aging analysis
 * - Loan summaries and statistics
 * - Data export utilities
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanReportingService {
    
    /**
     * Generate loan portfolio summary
     */
    def generatePortfolioSummary(Branch branch = null, Date asOfDate = null) {
        log.info("Generating loan portfolio summary for branch: ${branch?.name ?: 'ALL'}")
        
        try {
            def criteria = Loan.createCriteria()
            def loans = criteria.list {
                if (branch) {
                    eq('branch', branch)
                }
                if (asOfDate) {
                    le('openingDate', asOfDate)
                }
                order('accountNo', 'asc')
            }
            
            def summary = [
                totalLoans: loans.size(),
                totalGrantedAmount: 0.00D,
                totalOutstandingBalance: 0.00D,
                totalInterestBalance: 0.00D,
                totalPenaltyBalance: 0.00D,
                byStatus: [:],
                byPerformanceClass: [:],
                byProduct: [:]
            ]
            
            for (loan in loans) {
                // Total amounts
                summary.totalGrantedAmount += loan.grantedAmount ?: 0
                summary.totalOutstandingBalance += loan.balanceAmount ?: 0
                summary.totalInterestBalance += loan.interestBalanceAmount ?: 0
                summary.totalPenaltyBalance += loan.penaltyBalanceAmount ?: 0
                
                // By status
                def statusKey = loan.status.description
                if (!summary.byStatus[statusKey]) {
                    summary.byStatus[statusKey] = [count: 0, amount: 0.00D]
                }
                summary.byStatus[statusKey].count++
                summary.byStatus[statusKey].amount += loan.balanceAmount ?: 0
                
                // By performance classification
                def perfKey = loan.performanceClassification?.description ?: 'Unclassified'
                if (!summary.byPerformanceClass[perfKey]) {
                    summary.byPerformanceClass[perfKey] = [count: 0, amount: 0.00D]
                }
                summary.byPerformanceClass[perfKey].count++
                summary.byPerformanceClass[perfKey].amount += loan.balanceAmount ?: 0
                
                // By product
                def productKey = loan.product?.name ?: 'Unknown'
                if (!summary.byProduct[productKey]) {
                    summary.byProduct[productKey] = [count: 0, amount: 0.00D]
                }
                summary.byProduct[productKey].count++
                summary.byProduct[productKey].amount += loan.balanceAmount ?: 0
            }
            
            log.info("Portfolio summary generated: ${summary.totalLoans} loans, ${summary.totalOutstandingBalance} outstanding")
            return summary
            
        } catch (Exception e) {
            log.error("Error generating portfolio summary: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate aging analysis report
     */
    def generateAgingAnalysis(Branch branch = null, Date asOfDate = null) {
        log.info("Generating aging analysis for branch: ${branch?.name ?: 'ALL'}")
        
        try {
            def currentDate = asOfDate ?: new Date()
            
            def criteria = Loan.createCriteria()
            def loans = criteria.list {
                if (branch) {
                    eq('branch', branch)
                }
                ne('status', LoanAcctStatus.get(6))  // Exclude closed loans
                order('accountNo', 'asc')
            }
            
            def agingBuckets = [
                current: [count: 0, amount: 0.00D],
                days1to30: [count: 0, amount: 0.00D],
                days31to60: [count: 0, amount: 0.00D],
                days61to90: [count: 0, amount: 0.00D],
                days91to180: [count: 0, amount: 0.00D],
                days181to365: [count: 0, amount: 0.00D],
                over365: [count: 0, amount: 0.00D]
            ]
            
            for (loan in loans) {
                def daysInArrears = calculateDaysInArrears(loan, currentDate)
                def outstandingAmount = loan.balanceAmount ?: 0
                
                if (daysInArrears <= 0) {
                    agingBuckets.current.count++
                    agingBuckets.current.amount += outstandingAmount
                } else if (daysInArrears <= 30) {
                    agingBuckets.days1to30.count++
                    agingBuckets.days1to30.amount += outstandingAmount
                } else if (daysInArrears <= 60) {
                    agingBuckets.days31to60.count++
                    agingBuckets.days31to60.amount += outstandingAmount
                } else if (daysInArrears <= 90) {
                    agingBuckets.days61to90.count++
                    agingBuckets.days61to90.amount += outstandingAmount
                } else if (daysInArrears <= 180) {
                    agingBuckets.days91to180.count++
                    agingBuckets.days91to180.amount += outstandingAmount
                } else if (daysInArrears <= 365) {
                    agingBuckets.days181to365.count++
                    agingBuckets.days181to365.amount += outstandingAmount
                } else {
                    agingBuckets.over365.count++
                    agingBuckets.over365.amount += outstandingAmount
                }
            }
            
            log.info("Aging analysis generated successfully")
            return agingBuckets
            
        } catch (Exception e) {
            log.error("Error generating aging analysis: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate loan performance report
     */
    def generatePerformanceReport(Branch branch = null, Date fromDate = null, Date toDate = null) {
        log.info("Generating loan performance report")
        
        try {
            def criteria = Loan.createCriteria()
            def loans = criteria.list {
                if (branch) {
                    eq('branch', branch)
                }
                if (fromDate) {
                    ge('openingDate', fromDate)
                }
                if (toDate) {
                    le('openingDate', toDate)
                }
                order('openingDate', 'desc')
            }
            
            def performance = [
                totalLoansOriginated: loans.size(),
                totalAmountOriginated: 0.00D,
                loansFullyPaid: 0,
                amountFullyPaid: 0.00D,
                loansWrittenOff: 0,
                amountWrittenOff: 0.00D,
                currentOutstanding: 0.00D,
                averageLoanSize: 0.00D,
                portfolioAtRisk: 0.00D,
                portfolioAtRiskPercentage: 0.00D
            ]
            
            def portfolioAtRiskAmount = 0.00D
            
            for (loan in loans) {
                performance.totalAmountOriginated += loan.grantedAmount ?: 0
                
                if (loan.status.id == 6) {  // Fully paid
                    performance.loansFullyPaid++
                    performance.amountFullyPaid += loan.grantedAmount ?: 0
                } else if (loan.status.id == 8) {  // Written off
                    performance.loansWrittenOff++
                    performance.amountWrittenOff += loan.grantedAmount ?: 0
                } else {
                    performance.currentOutstanding += loan.balanceAmount ?: 0
                    
                    // Check if loan is at risk (>30 days past due)
                    def daysInArrears = calculateDaysInArrears(loan, new Date())
                    if (daysInArrears > 30) {
                        portfolioAtRiskAmount += loan.balanceAmount ?: 0
                    }
                }
            }
            
            if (performance.totalLoansOriginated > 0) {
                performance.averageLoanSize = performance.totalAmountOriginated / performance.totalLoansOriginated
            }
            
            if (performance.currentOutstanding > 0) {
                performance.portfolioAtRisk = portfolioAtRiskAmount
                performance.portfolioAtRiskPercentage = (portfolioAtRiskAmount / performance.currentOutstanding) * 100
            }
            
            log.info("Performance report generated successfully")
            return performance
            
        } catch (Exception e) {
            log.error("Error generating performance report: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate loan maturity report
     */
    def generateMaturityReport(Branch branch = null, Date fromDate = null, Date toDate = null) {
        log.info("Generating loan maturity report")
        
        try {
            def criteria = Loan.createCriteria()
            def loans = criteria.list {
                if (branch) {
                    eq('branch', branch)
                }
                if (fromDate) {
                    ge('maturityDate', fromDate)
                }
                if (toDate) {
                    le('maturityDate', toDate)
                }
                ne('status', LoanAcctStatus.get(6))  // Exclude closed loans
                order('maturityDate', 'asc')
            }
            
            def maturityData = []
            
            for (loan in loans) {
                maturityData.add([
                    accountNo: loan.accountNo,
                    customerName: loan.customer?.name1,
                    grantedAmount: loan.grantedAmount,
                    outstandingBalance: loan.balanceAmount,
                    maturityDate: loan.maturityDate,
                    daysToMaturity: loan.maturityDate ? (loan.maturityDate - new Date()) : null,
                    status: loan.status.description,
                    product: loan.product?.name
                ])
            }
            
            log.info("Maturity report generated: ${maturityData.size()} loans")
            return maturityData
            
        } catch (Exception e) {
            log.error("Error generating maturity report: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate loan transaction history
     */
    def generateTransactionHistory(Loan loanInstance, Date fromDate = null, Date toDate = null) {
        log.info("Generating transaction history for loan: ${loanInstance?.accountNo}")
        
        try {
            def criteria = LoanLedger.createCriteria()
            def transactions = criteria.list {
                eq('loan', loanInstance)
                if (fromDate) {
                    ge('txnDate', fromDate)
                }
                if (toDate) {
                    le('txnDate', toDate)
                }
                order('txnDate', 'desc')
                order('id', 'desc')
            }
            
            def transactionData = []
            
            for (transaction in transactions) {
                transactionData.add([
                    txnDate: transaction.txnDate,
                    txnRef: transaction.txnRef,
                    txnParticulars: transaction.txnParticulars,
                    principalDebit: transaction.principalDebit,
                    principalCredit: transaction.principalCredit,
                    interestDebit: transaction.interestDebit,
                    interestCredit: transaction.interestCredit,
                    penaltyDebit: transaction.penaltyDebit,
                    penaltyCredit: transaction.penaltyCredit,
                    principalBalance: transaction.principalBalance,
                    txnCode: transaction.txnCode
                ])
            }
            
            log.info("Transaction history generated: ${transactionData.size()} transactions")
            return transactionData
            
        } catch (Exception e) {
            log.error("Error generating transaction history: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate installment schedule report
     */
    def generateInstallmentScheduleReport(Loan loanInstance) {
        log.info("Generating installment schedule for loan: ${loanInstance?.accountNo}")
        
        try {
            def installments = loanInstance.loanInstallments.sort { it.sequenceNo }
            def scheduleData = []
            
            for (installment in installments) {
                def paidAmount = (installment.principalInstallmentPaid ?: 0) +
                               (installment.interestInstallmentPaid ?: 0) +
                               (installment.penaltyInstallmentPaid ?: 0) +
                               (installment.serviceChargeInstallmentPaid ?: 0)
                
                def balanceAmount = installment.totalInstallmentAmount - paidAmount
                
                scheduleData.add([
                    sequenceNo: installment.sequenceNo,
                    installmentDate: installment.installmentDate,
                    principalAmount: installment.principalInstallmentAmount,
                    interestAmount: installment.interestInstallmentAmount,
                    serviceChargeAmount: installment.serviceChargeInstallmentAmount,
                    totalAmount: installment.totalInstallmentAmount,
                    paidAmount: paidAmount,
                    balanceAmount: balanceAmount,
                    status: installment.status.description,
                    daysOverdue: installment.installmentDate < new Date() ? 
                                (new Date() - installment.installmentDate) : 0
                ])
            }
            
            log.info("Installment schedule generated: ${scheduleData.size()} installments")
            return scheduleData
            
        } catch (Exception e) {
            log.error("Error generating installment schedule: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate days in arrears for a loan
     */
    def calculateDaysInArrears(Loan loanInstance, Date asOfDate = null) {
        def currentDate = asOfDate ?: new Date()
        def maxDaysOverdue = 0
        
        def overdueInstallments = loanInstance.loanInstallments.findAll { 
            it.installmentDate < currentDate && it.status.id == 2  // Unpaid
        }
        
        for (installment in overdueInstallments) {
            def daysOverdue = currentDate - installment.installmentDate
            if (daysOverdue > maxDaysOverdue) {
                maxDaysOverdue = daysOverdue
            }
        }
        
        return maxDaysOverdue
    }

    /**
     * Generate loan summary statistics
     */
    def generateLoanStatistics(Branch branch = null) {
        log.info("Generating loan statistics")
        
        try {
            def criteria = Loan.createCriteria()
            def results = criteria.get {
                if (branch) {
                    eq('branch', branch)
                }
                projections {
                    count('id')
                    sum('grantedAmount')
                    sum('balanceAmount')
                    avg('grantedAmount')
                    max('grantedAmount')
                    min('grantedAmount')
                }
            }
            
            def statistics = [
                totalLoans: results[0] ?: 0,
                totalGrantedAmount: results[1] ?: 0.00D,
                totalOutstandingBalance: results[2] ?: 0.00D,
                averageLoanSize: results[3] ?: 0.00D,
                largestLoan: results[4] ?: 0.00D,
                smallestLoan: results[5] ?: 0.00D,
                averageOutstandingBalance: 0.00D
            ]
            
            if (statistics.totalLoans > 0) {
                statistics.averageOutstandingBalance = statistics.totalOutstandingBalance / statistics.totalLoans
            }
            
            log.info("Loan statistics generated successfully")
            return statistics
            
        } catch (Exception e) {
            log.error("Error generating loan statistics: ${e.message}", e)
            throw e
        }
    }

    /**
     * Export loan data to CSV format
     */
    def exportLoanDataToCSV(List<Loan> loans) {
        log.info("Exporting ${loans.size()} loans to CSV format")
        
        try {
            def csvData = []
            
            // Header row
            csvData.add([
                'Account No', 'Customer Name', 'Product', 'Branch', 'Granted Amount',
                'Outstanding Balance', 'Interest Balance', 'Penalty Balance',
                'Opening Date', 'Maturity Date', 'Status', 'Performance Class',
                'Interest Rate', 'Term', 'Installments'
            ])
            
            // Data rows
            for (loan in loans) {
                csvData.add([
                    loan.accountNo,
                    loan.customer?.name1,
                    loan.product?.name,
                    loan.branch?.name,
                    loan.grantedAmount,
                    loan.balanceAmount,
                    loan.interestBalanceAmount,
                    loan.penaltyBalanceAmount,
                    loan.openingDate,
                    loan.maturityDate,
                    loan.status?.description,
                    loan.performanceClassification?.description,
                    loan.interestRate,
                    loan.term,
                    loan.numInstallments
                ])
            }
            
            log.info("CSV export completed successfully")
            return csvData
            
        } catch (Exception e) {
            log.error("Error exporting loan data to CSV: ${e.message}", e)
            throw e
        }
    }
}
