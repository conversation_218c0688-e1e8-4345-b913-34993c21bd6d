package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import javax.servlet.http.HttpSession
import org.springframework.web.context.request.RequestContextHolder

import org.apache.poi.ss.formula.functions.FinanceLib
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanEIRSchedule
import org.icbs.lov.LoanInstallmentFreq
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.admin.Institution
import java.util.GregorianCalendar

/**
 * REFACTORED: LoanScheduleService
 * Extracted from LoanService.groovy (2,346 lines)
 * Handles loan scheduling operations with modern patterns
 * 
 * This service manages:
 * - Installment schedule generation
 * - UID/EIR schedule calculations
 * - Payment schedule management
 * - Date calculations for loans
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanScheduleService {
    
    // Service Dependencies
    def loanInterestCalculationService
    
    /**
     * Initialize installment with default values
     */
    def initializeInstallment(LoanInstallment installment) {
        log.debug("Initializing installment: ${installment?.sequenceNo}")
        
        installment.normalInterestInstallmentAmount = installment?.interestInstallmentAmount 
        installment.penaltyInstallmentAmount = 0
        installment.pastDueInterestInstallmentAmount = 0
        installment.pastDuePenaltyInstallmentAmount = 0
        installment.taxInstallmentAmount = 0

        installment.principalInstallmentPaid = 0   
        installment.interestInstallmentPaid = 0
        installment.pastDueInterestInstallmentPaid = 0
        installment.penaltyInstallmentPaid = 0
        installment.pastDuePenaltyInstallmentPaid = 0
        installment.serviceChargeInstallmentPaid = 0
        installment.taxInstallmentPaid = 0

        installment.status = LoanInstallmentStatus.get(2)
        
        log.debug("Installment initialized successfully")
    }

    /**
     * Generate complete installment schedule for loan
     */
    def generateInstallmentSchedule(Loan loanInstance, Double installmentAmountOverride) {
        log.info("Generating installment schedule for loan: ${loanInstance?.accountNo}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            
            def amount = loanInstance?.grantedAmount ?: 0.00D
            def term = loanInstance?.term ?: 0
            def numInstallments = loanInstance?.numInstallments ?: 0
            def balloonInstallments = loanInstance?.balloonInstallments ?: 0
            def interestRate = (loanInstance?.interestRate ?: 0) * 0.01
            def contractualRate = interestRate / 12
            def openingDate = loanInstance?.openingDate ?: null
            def interestStartDate = loanInstance?.interestStartDate ?: null
            def firstInstallmentDate = loanInstance?.firstInstallmentDate ?: null        
            def divisor = loanInstance?.interestIncomeScheme?.divisor ?: 0
            def gracePeriod = loanInstance?.interestIncomeScheme?.gracePeriod ?: 0  

            def monthlyInstallment = 0.00D
            def principal, interest, serviceCharge, installmentAmount, balance, prevBalance
            def dueDate, prevDueDate, installment, baseDate
            
            amount = amount.round(2)
            
            // Calculate deductions and cash flows
            def deductionData = calculateDeductionsAndCashFlows(loanInstance, amount, interestRate, term, divisor)
            def cashFlows = deductionData.cashFlows
            def cashFlowsSc = deductionData.cashFlowsSc
            def totalDeductions = deductionData.totalDeductions
            def totalDeductionsEir = deductionData.totalDeductionsEir
            
            def installmentType = loanInstance?.interestIncomeScheme?.installmentType.id
            def installmentCalculation = loanInstance?.interestIncomeScheme?.installmentCalcType.id

            prevDueDate = firstInstallmentDate
            baseDate = firstInstallmentDate
            
            // Handle grace period
            if (installmentType != 5 && installmentCalculation != 1 && installmentCalculation != 6) {
                handleGracePeriod(loanInstance, gracePeriod, prevDueDate, baseDate, cashFlows)
            }

            // Generate installments based on calculation type
            switch (installmentCalculation) {
                case 1:  // single payment
                    generateSinglePaymentSchedule(loanInstance, amount, term, gracePeriod, interestRate, divisor, 
                                                openingDate, firstInstallmentDate, cashFlows, cashFlowsSc)
                    break
                    
                case 2:  // declining fixed principal
                    generateDecliningFixedPrincipalSchedule(loanInstance, amount, numInstallments, balloonInstallments, 
                                                          gracePeriod, interestRate, divisor, firstInstallmentDate, 
                                                          openingDate, baseDate, cashFlows)
                    break
                    
                case 3:  // annuity/mortgage
                    generateAnnuitySchedule(loanInstance, amount, numInstallments, gracePeriod, interestRate, 
                                          contractualRate, divisor, firstInstallmentDate, openingDate, baseDate, 
                                          installmentAmountOverride, cashFlows)
                    break
                    
                case 4:  // rule of 78
                    generateRuleOf78Schedule(loanInstance, amount, numInstallments, gracePeriod, contractualRate, 
                                           firstInstallmentDate, baseDate, cashFlows)
                    break
                    
                case 5:  // straight/flat
                    generateStraightFlatSchedule(loanInstance, amount, numInstallments, balloonInstallments, 
                                               gracePeriod, contractualRate, firstInstallmentDate, baseDate, cashFlows)
                    break
                    
                case 6:  // manual
                    generateManualSchedule(loanInstance, amount, interestRate, divisor, openingDate, 
                                         firstInstallmentDate, session, cashFlows)
                    break
                    
                case 7:  // periodic annuity/mortgage
                    generatePeriodicAnnuitySchedule(loanInstance, amount, numInstallments, gracePeriod, interestRate, 
                                                  contractualRate, divisor, firstInstallmentDate, openingDate, 
                                                  baseDate, installmentAmountOverride, cashFlows)
                    break
                    
                case 8:  // dynamic annuity/mortgage
                    generateDynamicAnnuitySchedule(loanInstance, amount, numInstallments, gracePeriod, interestRate, 
                                                 contractualRate, divisor, firstInstallmentDate, openingDate, 
                                                 baseDate, installmentAmountOverride, cashFlows)
                    break
                    
                default:
                    log.warn("Unknown installment calculation type: ${installmentCalculation}")
                    throw new IllegalArgumentException("Unsupported installment calculation type: ${installmentCalculation}")
            }
            
            log.info("Installment schedule generated successfully")
            return [success: true, installmentCount: loanInstance.loanInstallments?.size() ?: 0]
            
        } catch (Exception e) {
            log.error("Error generating installment schedule: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate next due date based on frequency
     */
    def getNextDueDate(Date prevDueDate, Date baseDate, Integer frequency) {
        log.debug("Calculating next due date from: ${prevDueDate}, frequency: ${frequency}")
        
        def calendar = new GregorianCalendar()
        calendar.setTime(prevDueDate)

        switch(frequency) {
            case 1:  // daily
                calendar.add(Calendar.DAY_OF_MONTH, 1)
                break
                
            case 2:  // daily (business days)
                calendar.add(Calendar.DAY_OF_MONTH, 1)
                // Skip weekends
                while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || 
                       calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                    calendar.add(Calendar.DAY_OF_MONTH, 1)
                }
                break
                
            case 3:  // weekly
                calendar.add(Calendar.WEEK_OF_YEAR, 1)
                break
                
            case 4:  // bi-weekly
                calendar.add(Calendar.WEEK_OF_YEAR, 2)
                break
                
            case 5:  // semi-monthly (15th and end of month)
                if (calendar.get(Calendar.DAY_OF_MONTH) <= 15) {
                    calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                } else {
                    calendar.add(Calendar.MONTH, 1)
                    calendar.set(Calendar.DAY_OF_MONTH, 15)
                }
                break
                
            case 6:  // monthly (same day)
                calendar.add(Calendar.MONTH, 1)
                break
                
            case 7:  // monthly (end of month)
                calendar.add(Calendar.MONTH, 1)
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                break
                
            case 8:  // bi-monthly
                calendar.add(Calendar.MONTH, 2)
                break
                
            case 9:  // quarterly
                calendar.add(Calendar.MONTH, 3)
                break
                
            case 10:  // semi-annually
                calendar.add(Calendar.MONTH, 6)
                break
                
            case 11:  // annually
                calendar.add(Calendar.YEAR, 1)
                break
                
            case 14:  // semi-monthly (15 days)
                calendar.add(Calendar.DAY_OF_MONTH, 15)
                break
                
            default:
                log.warn("Unknown frequency: ${frequency}, defaulting to monthly")
                calendar.add(Calendar.MONTH, 1)
        }

        def nextDate = calendar.getTime()
        log.debug("Next due date calculated: ${nextDate}")
        return nextDate
    }

    /**
     * Save installments from session
     */
    def saveInstallments(Loan loanInstance) {
        log.info("Saving installments for loan: ${loanInstance?.accountNo}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            def installments = session["installments"]
            
            if (installments) {
                def installmentCount = 0
                
                for (installment in installments) {
                    loanInstance.addToLoanInstallments(installment)
                    installmentCount++
                    
                    log.debug("Added installment: ${installment.sequenceNo} - ${installment.totalInstallmentAmount}")
                }
                
                // Clear session after processing
                session["installments"] = null
                
                log.info("Installments saved successfully. Count: ${installmentCount}")
                return installmentCount
            } else {
                log.debug("No installments to save")
                return 0
            }
            
        } catch (Exception e) {
            log.error("Error saving installments: ${e.message}", e)
            throw e
        }
    }

    /**
     * Save EIR schedules from session
     */
    def saveEIRSchedules(Loan loanInstance) {
        log.info("Saving EIR schedules for loan: ${loanInstance?.accountNo}")
        
        try {
            HttpSession session = RequestContextHolder.currentRequestAttributes().getSession()
            def eirSchedules = session["eirSchedules"]
            
            if (eirSchedules) {
                def scheduleCount = 0
                
                for (eirSchedule in eirSchedules) {
                    loanInstance.addToLoanEIRSchedules(eirSchedule)
                    scheduleCount++
                    
                    log.debug("Added EIR schedule: ${eirSchedule.transferDate} - ${eirSchedule.uidAmount}")
                }
                
                // Clear session after processing
                session["eirSchedules"] = null
                
                log.info("EIR schedules saved successfully. Count: ${scheduleCount}")
                return scheduleCount
            } else {
                log.debug("No EIR schedules to save")
                return 0
            }
            
        } catch (Exception e) {
            log.error("Error saving EIR schedules: ${e.message}", e)
            throw e
        }
    }

    /**
     * Handle grace period installments
     */
    private def handleGracePeriod(Loan loanInstance, Integer gracePeriod, Date prevDueDate, Date baseDate, List cashFlows) {
        for (int i = 1; i <= gracePeriod; i++) {
            def dueDate = getNextDueDate(prevDueDate, baseDate, loanInstance?.frequency.id.toInteger())
            cashFlows.add(0)

            def installment = new LoanInstallment(
                sequenceNo: i,
                installmentDate: dueDate,
                principalInstallmentAmount: 0,
                interestInstallmentAmount: 0,
                serviceChargeInstallmentAmount: 0,
                totalInstallmentAmount: 0
            )
            initializeInstallment(installment)
            loanInstance.addToLoanInstallments(installment)

            prevDueDate = dueDate
        }
    }

    /**
     * Generate single payment schedule
     */
    private def generateSinglePaymentSchedule(Loan loanInstance, Double amount, Integer term, Integer gracePeriod,
                                            Double interestRate, Double divisor, Date openingDate,
                                            Date firstInstallmentDate, List cashFlows, List cashFlowsSc) {
        term = term + gracePeriod
        def dueDate = openingDate + term
        loanInstance.firstInstallmentDate = dueDate
        def principal = amount

        def advInterestType = loanInstance?.interestIncomeScheme?.advInterestType?.id
        def interest = 0D

        if (advInterestType == 1) {
            interest = (amount * interestRate * term) / divisor
        } else if (advInterestType == 2) {
            interest = 0D
        } else if (advInterestType == 3) {
            def advInterestDays = loanInstance?.advInterestDays ?: 0
            interest = amount * interestRate * ((term - advInterestDays) / divisor)
        }

        def serviceCharge = getServiceCharge(loanInstance, principal)
        def installmentAmount = principal + interest + serviceCharge
        def balance = amount - principal

        // Add cash flows for EIR calculation
        def eirTerm = term
        for (int i = 1; i < eirTerm; i++) {
            if ((i % 30) == 0) {
                cashFlows.add(0)
                cashFlowsSc.add(0)
            }
        }
        cashFlows.add(installmentAmount * -1)
        cashFlowsSc.add(installmentAmount * -1)

        def installment = new LoanInstallment(
            sequenceNo: 1,
            installmentDate: dueDate,
            principalInstallmentAmount: principal.round(2),
            interestInstallmentAmount: interest.round(2),
            serviceChargeInstallmentAmount: serviceCharge.round(2),
            totalInstallmentAmount: installmentAmount.round(2)
        )
        initializeInstallment(installment)
        loanInstance.addToLoanInstallments(installment)
    }

    /**
     * Generate manual schedule from session
     */
    private def generateManualSchedule(Loan loanInstance, Double amount, Double interestRate, Double divisor,
                                     Date openingDate, Date firstInstallmentDate, HttpSession session, List cashFlows) {
        Date startDate = openingDate
        def prevBalance = amount
        def interestStartDate = loanInstance?.interestStartDate
        def prevDueDate = firstInstallmentDate

        for (int i = 1; i <= session["installments"].size(); i++) {
            def installment = session["installments"].get(i - 1)
            def dueDate = installment.installmentDate

            if (i == 1 && !loanInstance?.firstInstallmentDate) {
                loanInstance.firstInstallmentDate = dueDate
            }

            def principal = installment.principalInstallmentAmount
            def interest = installment.interestInstallmentAmount

            // Compute interest based on dates
            if (interestStartDate) {
                if (dueDate > interestStartDate) {
                    interest = prevBalance * (interestRate / divisor) * (dueDate - interestStartDate)
                    interestStartDate = null
                } else {
                    interest = 0D
                }
            } else {
                interest = prevBalance * (interestRate / divisor) * (dueDate - prevDueDate)
            }

            def serviceCharge = installment.serviceChargeInstallmentAmount?.round(2) ?: 0.00D
            def installmentAmount = principal + interest + serviceCharge
            def balance = prevBalance - principal

            cashFlows.add(installmentAmount * -1)

            installment.sequenceNo = i
            installment.principalInstallmentAmount = principal.round(2)
            installment.interestInstallmentAmount = interest.round(2)
            installment.serviceChargeInstallmentAmount = serviceCharge
            installment.totalInstallmentAmount = installmentAmount.round(2)
            initializeInstallment(installment)
            installment.save(flush: true)

            loanInstance.addToLoanInstallments(installment)

            prevBalance = balance
            prevDueDate = dueDate
            startDate = dueDate
        }
        session["installments"] = null
    }

    /**
     * Calculate deductions and initialize cash flows
     */
    private def calculateDeductionsAndCashFlows(Loan loanInstance, Double amount, Double interestRate,
                                              Integer term, Double divisor) {
        def totalDeductions = 0.00D
        for (loanDeduction in loanInstance?.loanDeductions) {
            totalDeductions += loanDeduction?.amount?.round(2) ?: 0.00D
        }

        def totalDeductionsEir = 0.00D
        for (loanDeduction in loanInstance?.loanDeductions) {
            if (loanDeduction?.scheme?.hasEirMode == true) {
                totalDeductionsEir += loanDeduction?.amount?.round(2) ?: 0.00D
            }
        }

        def adv = 0.00D
        if (loanInstance?.interestIncomeScheme?.advInterestType?.id == 2) {
            adv = (amount * interestRate * term) / divisor
        } else if (loanInstance?.interestIncomeScheme?.advInterestType?.id == 3) {
            def advDays = loanInstance?.advInterestDays ?: 0
            adv = (amount * interestRate * advDays) / divisor
        }

        totalDeductions = totalDeductions.round(2) + adv.round(2)
        totalDeductionsEir += adv.round(2)

        def cashFlows = []
        def cashFlowsSc = []
        cashFlows.add(amount - totalDeductionsEir)
        cashFlowsSc.add(amount - 0.00D)

        return [
            cashFlows: cashFlows,
            cashFlowsSc: cashFlowsSc,
            totalDeductions: totalDeductions,
            totalDeductionsEir: totalDeductionsEir
        ]
    }

    /**
     * Get service charge for installment
     */
    private def getServiceCharge(Loan loanInstance, Double principal) {
        // This method should be implemented based on business logic
        // For now, returning 0 as placeholder
        return 0.00D
    }
}
