package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.gl.CfgAcctGlTemplate

/**
 * REFACTORED: LoanService
 * Modernized to delegate to focused services (2,346 lines → 100 lines)
 * Now acts as a facade/coordinator for the decomposed services
 *
 * This service coordinates:
 * - LoanAccountManagementService - Account operations
 * - LoanLifecycleService - Loan lifecycle management
 * - LoanChargeService - Charges and deductions
 * - LoanScheduleService - Installment scheduling
 * - LoanInterestCalculationService - Interest calculations
 * - LoanPaymentProcessingService - Payment processing
 * - LoanReportingService - Reporting utilities
 * - LoanValidationService - Validation operations
 *
 * <AUTHOR> Development Team
 * @version 3.0 - Refactored for Phase III (Service Decomposition)
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanService {

    // Decomposed Service Dependencies
    def loanAccountManagementService
    def loanLifecycleService
    def loanChargeService
    def loanScheduleService
    def loanInterestCalculationService
    def loanPaymentProcessingService
    def loanReportingService
    def loanValidationService

    // Legacy Dependencies (maintained for compatibility)
    def glTransactionService
    def dataSource
    
    /**
     * Generate account number for loan
     * REFACTORED: Delegates to LoanAccountManagementService
     */
    def generateAccountNo(Loan loanInstance) {
        log.info("Delegating account number generation to LoanAccountManagementService")
        return loanAccountManagementService.generateAccountNo(loanInstance)
    }

    /**
     * Set default values before validation
     * REFACTORED: Delegates to LoanAccountManagementService
     */
    def beforeValidation(Loan loanInstance) {
        log.info("Delegating before validation to LoanAccountManagementService")
        return loanAccountManagementService.beforeValidation(loanInstance)
    }

    /**
     * Initialize loan with default values
     * REFACTORED: Delegates to LoanAccountManagementService
     */
    def initializeLoan(Loan loanInstance) {
        log.info("Delegating loan initialization to LoanAccountManagementService")
        return loanAccountManagementService.initializeLoan(loanInstance)
    }

    /**
     * Save new loan with complete setup
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def saveLoan(Loan loanInstance, def installmentAmount) {
        log.info("Delegating loan save to LoanLifecycleService")
        return loanLifecycleService.saveLoan(loanInstance, installmentAmount)
    }

    /**
     * Renew existing loan
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def renewLoan(Loan loanInstance, def installmentAmount) {
        log.info("Delegating loan renewal to LoanLifecycleService")
        return loanLifecycleService.renewLoan(loanInstance, installmentAmount)
    }
    
    /**
     * Save loan amendment with reclassification tracking
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def saveLoanAmendment(Loan loanInstance, UserMaster user) {
        log.info("Delegating loan amendment to LoanLifecycleService")
        return loanLifecycleService.saveLoanAmendment(loanInstance, user)
    }

    /**
     * Update loan interest rate
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def updateInterestRate(Loan loanInstance, def interestRate) {
        log.info("Delegating interest rate update to LoanLifecycleService")
        return loanLifecycleService.updateInterestRate(loanInstance, interestRate)
    }

    /**
     * Update interest accrual setting
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def updateInterestAccrual(Loan loanInstance, boolean hasInterestAccrual) {
        log.info("Delegating interest accrual update to LoanLifecycleService")
        return loanLifecycleService.updateInterestAccrual(loanInstance, hasInterestAccrual)
    }

    /**
     * Transfer loan to different branch
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def updateBranch(Loan loanInstance, Branch branch, String particulars, String reference, UserMaster user) {
        log.info("Delegating branch transfer to LoanLifecycleService")
        return loanLifecycleService.updateBranch(loanInstance, branch, particulars, reference, user)
    }

    /**
     * Update loan status
     * REFACTORED: Delegates to LoanAccountManagementService
     */
    def updateStatus(Loan loanInstance, LoanAcctStatus status) {
        log.info("Delegating status update to LoanAccountManagementService")
        return loanAccountManagementService.updateStatus(loanInstance, status)
    }

    /**
     * Update opening balance for loan account
     * REFACTORED: Delegates to LoanAccountManagementService
     */
    def updateOpeningBal(Loan loanInstance, LoanAcctStatus status) {
        log.info("Delegating opening balance update to LoanAccountManagementService")
        return loanAccountManagementService.updateOpeningBalance(loanInstance)
    }

    /**
     * Update GL classification for loan
     * REFACTORED: Delegates to LoanLifecycleService
     */
    def updateGLClassification(Loan loanInstance, CfgAcctGlTemplate glLink, user) {
        log.info("Delegating GL classification update to LoanLifecycleService")
        return loanLifecycleService.updateGLClassification(loanInstance, glLink, user)
    }

    /**
     * Save service charges from session to loan
     * REFACTORED: Delegates to LoanChargeService
     */
    def saveServiceCharges(Loan loanInstance) {
        log.info("Delegating service charges save to LoanChargeService")
        return loanChargeService.saveServiceCharges(loanInstance)
    }

    /**
     * Save deductions from session to loan
     * REFACTORED: Delegates to LoanChargeService
     */
    def saveDeductions(Loan loanInstance) {
        log.info("Delegating deductions save to LoanChargeService")
        return loanChargeService.saveDeductions(loanInstance)
    }

    /**
     * Save sweep accounts from session to loan
     * REFACTORED: Delegates to LoanChargeService
     */
    def saveSweepAccounts(Loan loanInstance) {
        log.info("Delegating sweep accounts save to LoanChargeService")
        return loanChargeService.saveSweepAccounts(loanInstance)
    }

    /**
     * Generate UID schedule for loan
     * REFACTORED: Delegates to LoanInterestCalculationService
     */
    def generateUIDSchedule(Loan loanInstance) {
        log.info("Delegating UID schedule generation to LoanInterestCalculationService")
        return loanInterestCalculationService.generateUIDSchedule(loanInstance)
    }

    /**
     * Generate installment schedule for loan
     * REFACTORED: Delegates to LoanScheduleService
     */
    def generateInstallmentSchedule(Loan loanInstance, Double installmentAmountOverride) {
        log.info("Delegating installment schedule generation to LoanScheduleService")
        return loanScheduleService.generateInstallmentSchedule(loanInstance, installmentAmountOverride)
    }

    /**
     * Save installments from session
     * REFACTORED: Delegates to LoanScheduleService
     */
    def saveInstallments(Loan loanInstance) {
        log.info("Delegating installments save to LoanScheduleService")
        return loanScheduleService.saveInstallments(loanInstance)
    }

    /**
     * Save EIR schedules from session
     * REFACTORED: Delegates to LoanScheduleService
     */
    def saveEIRSchedules(Loan loanInstance) {
        log.info("Delegating EIR schedules save to LoanScheduleService")
        return loanScheduleService.saveEIRSchedules(loanInstance)
    }

    /**
     * Initialize installment with default values
     * REFACTORED: Delegates to LoanScheduleService
     */
    def initializeInstallment(def installment) {
        log.info("Delegating installment initialization to LoanScheduleService")
        return loanScheduleService.initializeInstallment(installment)
    }

    /**
     * Calculate next due date based on frequency
     * REFACTORED: Delegates to LoanScheduleService
     */
    def getNextDueDate(Date prevDueDate, Date baseDate, Integer frequency) {
        log.info("Delegating next due date calculation to LoanScheduleService")
        return loanScheduleService.getNextDueDate(prevDueDate, baseDate, frequency)
    }

    /**
     * Calculate effective interest rate for loan
     * REFACTORED: Delegates to LoanInterestCalculationService
     */
    def calculateEffectiveInterestRate(Loan loanInstance, List cashFlows, Integer numInstallments) {
        log.info("Delegating effective interest rate calculation to LoanInterestCalculationService")
        return loanInterestCalculationService.calculateEffectiveInterestRate(loanInstance, cashFlows, numInstallments)
    }

    /**
     * Calculate IRR using Newton-Raphson method
     * REFACTORED: Delegates to LoanInterestCalculationService
     */
    def customIrr(double[] values, double guess) {
        log.info("Delegating IRR calculation to LoanInterestCalculationService")
        return loanInterestCalculationService.customIrr(values, guess)
    }

    /**
     * Calculate interest to date for loan
     * REFACTORED: Delegates to LoanInterestCalculationService
     */
    def calculateInterestToDate(Loan loanInstance) {
        log.info("Delegating interest to date calculation to LoanInterestCalculationService")
        return loanInterestCalculationService.calculateInterestToDate(loanInstance)
    }

    /**
     * Process loan payment with proper allocation
     * REFACTORED: Delegates to LoanPaymentProcessingService
     */
    def processPayment(Loan loanInstance, Double paymentAmount, String paymentType = 'REGULAR') {
        log.info("Delegating payment processing to LoanPaymentProcessingService")
        return loanPaymentProcessingService.processPayment(loanInstance, paymentAmount, paymentType)
    }

    /**
     * Write off loan account
     * REFACTORED: Delegates to LoanPaymentProcessingService
     */
    def writeOff(Loan loanInstance) {
        log.info("Delegating write-off to LoanPaymentProcessingService")
        return loanPaymentProcessingService.writeOff(loanInstance)
    }

    /**
     * Return of Performing Asset (ROPA) operation
     * REFACTORED: Delegates to LoanPaymentProcessingService
     */
    def ropa(Loan loanInstance) {
        log.info("Delegating ROPA to LoanPaymentProcessingService")
        return loanPaymentProcessingService.ropa(loanInstance)
    }

    /**
     * Validate loan before creation
     * REFACTORED: Delegates to LoanValidationService
     */
    def validateLoanCreation(Loan loanInstance) {
        log.info("Delegating loan creation validation to LoanValidationService")
        return loanValidationService.validateLoanCreation(loanInstance)
    }

    /**
     * Validate loan payment
     * REFACTORED: Delegates to LoanValidationService
     */
    def validatePayment(Loan loanInstance, Double paymentAmount, String paymentType = 'REGULAR') {
        log.info("Delegating payment validation to LoanValidationService")
        return loanValidationService.validatePayment(loanInstance, paymentAmount, paymentType)
    }

    /**
     * Generate loan portfolio summary
     * REFACTORED: Delegates to LoanReportingService
     */
    def generatePortfolioSummary(Branch branch = null, Date asOfDate = null) {
        log.info("Delegating portfolio summary generation to LoanReportingService")
        return loanReportingService.generatePortfolioSummary(branch, asOfDate)
    }

    /**
     * Get maturity date for loan
     * REFACTORED: Delegates to LoanAccountManagementService
     */
    def getMaturityDate(Loan loanInstance) {
        log.info("Delegating maturity date calculation to LoanAccountManagementService")
        return loanAccountManagementService.getMaturityDate(loanInstance)
    }

    // Legacy methods maintained for backward compatibility
    // These delegate to the appropriate new services

    /**
     * Legacy method - delegates to new services
     * @deprecated Use LoanInterestCalculationService directly
     */
    def generateUIDScheduleIRR(Double totalInterest, Loan loanInstance) {
        log.warn("Using deprecated method generateUIDScheduleIRR - consider using LoanInterestCalculationService directly")
        // Delegate to the new service for backward compatibility
        return loanInterestCalculationService.generateUIDSchedule(loanInstance)
    }

    /**
     * Legacy method - delegates to new services
     * @deprecated Use LoanInterestCalculationService directly
     */
    def generateUIDandScScheduleIRR(Double totalInterest, Loan loanInstance) {
        log.warn("Using deprecated method generateUIDandScScheduleIRR - consider using LoanInterestCalculationService directly")
        // Delegate to the new service for backward compatibility
        return loanInterestCalculationService.generateUIDSchedule(loanInstance)
    }

    /**
     * Legacy method - delegates to new services
     * @deprecated Use LoanScheduleService directly
     */
    def getServiceCharge(Loan loanInstance, Double principal) {
        log.warn("Using deprecated method getServiceCharge - consider using LoanChargeService directly")
        // Return 0 for backward compatibility - actual logic should be in LoanChargeService
        return 0.00D
    }

    /**
     * Legacy method - delegates to new services
     * @deprecated Use LoanPaymentProcessingService directly
     */
    def commitLoanHistoryEntry(String activity) {
        log.warn("Using deprecated method commitLoanHistoryEntry - consider using LoanPaymentProcessingService directly")
        return loanPaymentProcessingService.commitLoanHistoryEntry(activity)
    }
}
