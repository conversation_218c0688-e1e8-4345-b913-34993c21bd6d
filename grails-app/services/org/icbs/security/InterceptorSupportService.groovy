package org.icbs.security

import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Module
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

/**
 * Interceptor Support Service for QwikBanka Core Banking System
 * Provides common functionality for modern interceptors
 * 
 * Features:
 * - Centralized caching logic
 * - User session management
 * - Security validation
 * - Performance optimization
 * - Audit logging support
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Slf4j
@Transactional
class InterceptorSupportService {
    
    // Injected services
    def cacheService
    def auditLogService
    
    // Cache configuration
    private static final int DEFAULT_CACHE_EXPIRY_MINUTES = 30
    private static final int SHORT_CACHE_EXPIRY_MINUTES = 5
    private static final int LONG_CACHE_EXPIRY_MINUTES = 60
    
    /**
     * Get user from cache or database
     */
    UserMaster getCachedUser(Long userId) {
        if (!userId) {
            return null
        }
        
        String cacheKey = "user_${userId}"
        
        UserMaster user = cacheService?.get(cacheKey)
        if (!user) {
            user = UserMaster.get(userId)
            if (user) {
                cacheService?.put(cacheKey, user, DEFAULT_CACHE_EXPIRY_MINUTES)
            }
        }
        
        return user
    }
    
    /**
     * Get branch from cache or database
     */
    Branch getCachedBranch(Long branchId) {
        if (!branchId) {
            return null
        }
        
        String cacheKey = "branch_${branchId}"
        
        Branch branch = cacheService?.get(cacheKey)
        if (!branch) {
            branch = Branch.get(branchId)
            if (branch) {
                cacheService?.put(cacheKey, branch, DEFAULT_CACHE_EXPIRY_MINUTES)
            }
        }
        
        return branch
    }
    
    /**
     * Get module from cache or database
     */
    Module getCachedModule(String uri) {
        if (!uri) {
            return null
        }
        
        String cacheKey = "module_${uri}"
        
        Module module = cacheService?.get(cacheKey)
        if (!module) {
            module = Module.findByUri(uri)
            if (module) {
                cacheService?.put(cacheKey, module, LONG_CACHE_EXPIRY_MINUTES)
            }
        }
        
        return module
    }
    
    /**
     * Check if user has permission for module with caching
     */
    Boolean hasPermissionCached(Long userId, Long moduleId, def roleModuleService) {
        if (!userId || !moduleId) {
            return false
        }
        
        String cacheKey = "permission_${userId}_${moduleId}"
        
        Boolean hasPermission = cacheService?.get(cacheKey)
        if (hasPermission == null) {
            Module module = Module.get(moduleId)
            hasPermission = roleModuleService?.hasPermission(module) ?: false
            cacheService?.put(cacheKey, hasPermission, DEFAULT_CACHE_EXPIRY_MINUTES)
        }
        
        return hasPermission
    }
    
    /**
     * Get menu data with caching
     */
    def getCachedMenuData(String moduleCode, def roleModuleService) {
        if (!moduleCode) {
            return null
        }
        
        String cacheKey = "menu_${moduleCode}"
        
        def menu = cacheService?.get(cacheKey)
        if (!menu) {
            menu = roleModuleService?.getMenu(moduleCode)
            if (menu) {
                cacheService?.put(cacheKey, menu, DEFAULT_CACHE_EXPIRY_MINUTES)
            }
        }
        
        return menu
    }
    
    /**
     * Get unread message count with caching
     */
    Integer getCachedUnreadMessageCount(UserMaster user) {
        if (!user) {
            return 0
        }
        
        String cacheKey = "unread_messages_${user.id}"
        
        Integer count = cacheService?.get(cacheKey)
        if (count == null) {
            count = org.icbs.admin.UserMessage.countByRecipientAndIsRead(user, false)
            cacheService?.put(cacheKey, count, SHORT_CACHE_EXPIRY_MINUTES)
        }
        
        return count
    }
    
    /**
     * Check if branch tellering is active with caching
     */
    Boolean isBranchTelleringActive(Long branchId) {
        if (!branchId) {
            return false
        }
        
        String cacheKey = "branch_tellering_${branchId}"
        
        Boolean isActive = cacheService?.get(cacheKey)
        if (isActive == null) {
            Branch branch = Branch.get(branchId)
            isActive = branch?.isTelleringActive ?: false
            cacheService?.put(cacheKey, isActive, SHORT_CACHE_EXPIRY_MINUTES)
        }
        
        return isActive
    }
    
    /**
     * Check if system is locked with caching
     */
    Boolean isSystemLocked() {
        String cacheKey = "system_lock_status"
        
        Boolean isLocked = cacheService?.get(cacheKey)
        if (isLocked == null) {
            def systemLock = org.icbs.admin.Institution.findByParamCode('GEN.10250')
            isLocked = systemLock?.paramValue == 'TRUE'
            cacheService?.put(cacheKey, isLocked, 1) // Cache for 1 minute only
        }
        
        return isLocked
    }
    
    /**
     * Validate business day
     */
    Boolean isValidBusinessDay(Branch branch) {
        if (!branch || !branch.runDate) {
            return false
        }
        
        try {
            Date currentDate = new Date()
            Date runDate = branch.runDate
            
            // Allow same day or future dates
            return runDate >= currentDate.clearTime()
            
        } catch (Exception e) {
            log.error("Error validating business day: ${e.message}", e)
            return false
        }
    }
    
    /**
     * Clean and parse request URI for module matching
     */
    String parseRequestURI(String requestURI) {
        if (!requestURI) {
            return ""
        }
        
        String uri = requestURI
        
        // Remove common prefixes and suffixes
        uri = uri.replace('.dispatch', '')
        uri = uri.replace('/icbs/', '/')
        uri = uri.replace('/grails/', '/')
        
        // Parse URI to get the relevant part
        String[] parts = uri.split('/')
        StringBuilder parsedUri = new StringBuilder()
        
        int slashCount = 0
        for (String part : parts) {
            if (part == '') {
                slashCount++
            }
            
            if (slashCount > 1) {
                parsedUri.append('/').append(part)
            }
        }
        
        String result = parsedUri.toString()
        
        // Remove trailing slash if present
        if (result.endsWith('/') && result.length() > 1) {
            result = result[0..-2]
        }
        
        return result
    }
    
    /**
     * Log security event with context
     */
    void logSecurityEvent(String eventType, String description, String userId, String ipAddress, Map additionalData = [:]) {
        try {
            Map eventData = [
                eventType: eventType,
                description: description,
                userId: userId,
                ipAddress: ipAddress,
                timestamp: new Date(),
                userAgent: additionalData.userAgent,
                sessionId: additionalData.sessionId,
                requestURI: additionalData.requestURI
            ]
            
            auditLogService?.logSecurityEvent(eventType, description, userId, ipAddress)
            
        } catch (Exception e) {
            log.error("Error logging security event: ${e.message}", e)
        }
    }
    
    /**
     * Clear user-related caches
     */
    void clearUserCaches(Long userId) {
        if (!userId) {
            return
        }
        
        try {
            // Clear user cache
            cacheService?.evict("user_${userId}")
            
            // Clear permission caches
            cacheService?.evictPattern("permission_${userId}_*")
            
            // Clear menu caches
            cacheService?.evictPattern("user_menu_${userId}")
            
            // Clear message count cache
            cacheService?.evict("unread_messages_${userId}")
            
        } catch (Exception e) {
            log.error("Error clearing user caches: ${e.message}", e)
        }
    }
    
    /**
     * Clear system-wide caches
     */
    void clearSystemCaches() {
        try {
            // Clear system status caches
            cacheService?.evict("system_lock_status")
            
            // Clear branch caches
            cacheService?.evictPattern("branch_*")
            
            // Clear module caches
            cacheService?.evictPattern("module_*")
            
        } catch (Exception e) {
            log.error("Error clearing system caches: ${e.message}", e)
        }
    }
}
