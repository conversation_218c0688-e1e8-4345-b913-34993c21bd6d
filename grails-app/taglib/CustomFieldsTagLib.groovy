
/**
 * Modern Custom Fields TagLib for QwikBanka Core Banking System
 * Enhanced with Bootstrap 5, accessibility, and modern patterns
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since Grails 6.2.3
 */
class CustomFieldsTagLib {

    static namespace = "qb"

    /**
     * Enhanced text field with modern Bootstrap 5 styling and validation
     * Usage: <qb:textField bean="${customer}" field="firstName" label="First Name" required="true"/>
     */
    def textField = { attrs, body ->
        validateRequiredAttributes(attrs, ['field'])

        String fieldName = attrs.field
        String label = attrs.label ?: fieldName.capitalize()
        boolean required = attrs.required == 'true' || attrs.required == true
        boolean readonly = attrs.readonly == 'true' || attrs.readonly == true
        String helpText = attrs.helpText
        String placeholder = attrs.placeholder ?: "Enter ${label.toLowerCase()}"

        // Build CSS classes
        List<String> cssClasses = ['form-control']
        if (hasFieldErrors(attrs)) {
            cssClasses << 'is-invalid'
        }
        if (attrs.cssClass) {
            cssClasses << attrs.cssClass
        }

        // Generate unique ID
        String fieldId = attrs.id ?: generateFieldId(fieldName)

        out << renderFormGroup {
            out << renderLabel(fieldId, label, required)
            out << renderInputGroup {
                out << g.textField([
                    id: fieldId,
                    name: fieldName,
                    value: attrs.value,
                    class: cssClasses.join(' '),
                    placeholder: placeholder,
                    readonly: readonly,
                    required: required,
                    'aria-describedby': helpText ? "${fieldId}-help" : null
                ] + filterHtmlAttributes(attrs))

                if (hasFieldErrors(attrs)) {
                    out << renderFieldErrors(attrs)
                }
            }

            if (helpText) {
                out << renderHelpText(fieldId, helpText)
            }
        }
    }
    /**
     * Enhanced text area with modern Bootstrap 5 styling
     * Usage: <qb:textArea bean="${customer}" field="notes" label="Notes" rows="4"/>
     */
    def textArea = { attrs, body ->
        validateRequiredAttributes(attrs, ['field'])

        String fieldName = attrs.field
        String label = attrs.label ?: fieldName.capitalize()
        boolean required = attrs.required == 'true' || attrs.required == true
        boolean readonly = attrs.readonly == 'true' || attrs.readonly == true
        String helpText = attrs.helpText
        String placeholder = attrs.placeholder ?: "Enter ${label.toLowerCase()}"
        int rows = attrs.rows ? attrs.rows as int : 3

        // Build CSS classes
        List<String> cssClasses = ['form-control']
        if (hasFieldErrors(attrs)) {
            cssClasses << 'is-invalid'
        }
        if (attrs.cssClass) {
            cssClasses << attrs.cssClass
        }

        // Generate unique ID
        String fieldId = attrs.id ?: generateFieldId(fieldName)

        out << renderFormGroup {
            out << renderLabel(fieldId, label, required)
            out << renderInputGroup {
                out << g.textArea([
                    id: fieldId,
                    name: fieldName,
                    value: attrs.value,
                    class: cssClasses.join(' '),
                    placeholder: placeholder,
                    readonly: readonly,
                    required: required,
                    rows: rows,
                    'aria-describedby': helpText ? "${fieldId}-help" : null
                ] + filterHtmlAttributes(attrs))

                if (hasFieldErrors(attrs)) {
                    out << renderFieldErrors(attrs)
                }
            }

            if (helpText) {
                out << renderHelpText(fieldId, helpText)
            }
        }
    }
    /**
     * Enhanced select field with modern Bootstrap 5 styling
     * Usage: <qb:select bean="${customer}" field="status" label="Status" from="${statusList}" optionKey="id" optionValue="name"/>
     */
    def select = { attrs, body ->
        validateRequiredAttributes(attrs, ['field'])

        String fieldName = attrs.field
        String label = attrs.label ?: fieldName.capitalize()
        boolean required = attrs.required == 'true' || attrs.required == true
        boolean readonly = attrs.readonly == 'true' || attrs.readonly == true
        String helpText = attrs.helpText
        String noSelection = attrs.noSelection ?: ['': "Select ${label}"]

        // Build CSS classes
        List<String> cssClasses = ['form-select']
        if (hasFieldErrors(attrs)) {
            cssClasses << 'is-invalid'
        }
        if (attrs.cssClass) {
            cssClasses << attrs.cssClass
        }

        // Generate unique ID
        String fieldId = attrs.id ?: generateFieldId(fieldName)

        out << renderFormGroup {
            out << renderLabel(fieldId, label, required)
            out << renderInputGroup {
                out << g.select([
                    id: fieldId,
                    name: fieldName,
                    value: attrs.value,
                    class: cssClasses.join(' '),
                    from: attrs.from,
                    optionKey: attrs.optionKey,
                    optionValue: attrs.optionValue,
                    noSelection: noSelection,
                    readonly: readonly,
                    required: required,
                    'aria-describedby': helpText ? "${fieldId}-help" : null
                ] + filterHtmlAttributes(attrs))

                if (hasFieldErrors(attrs)) {
                    out << renderFieldErrors(attrs)
                }
            }

            if (helpText) {
                out << renderHelpText(fieldId, helpText)
            }
        }
    }

    /**
     * Enhanced date picker with modern styling and validation
     * Usage: <qb:datePicker bean="${customer}" field="birthDate" label="Birth Date" required="true"/>
     */
    def datePicker = { attrs, body ->
        validateRequiredAttributes(attrs, ['field'])

        String fieldName = attrs.field
        String label = attrs.label ?: fieldName.capitalize()
        boolean required = attrs.required == 'true' || attrs.required == true
        boolean readonly = attrs.readonly == 'true' || attrs.readonly == true
        String helpText = attrs.helpText
        String placeholder = attrs.placeholder ?: "MM/dd/yyyy"

        // Format date value
        String dateValue = ""
        if (attrs.value) {
            if (attrs.value instanceof Date) {
                dateValue = attrs.value.format("MM/dd/yyyy")
            } else {
                dateValue = attrs.value.toString()
            }
        }

        // Build CSS classes
        List<String> cssClasses = ['form-control', 'datepicker']
        if (hasFieldErrors(attrs)) {
            cssClasses << 'is-invalid'
        }
        if (attrs.cssClass) {
            cssClasses << attrs.cssClass
        }

        // Generate unique ID
        String fieldId = attrs.id ?: generateFieldId(fieldName)

        out << renderFormGroup {
            out << renderLabel(fieldId, label, required)
            out << renderInputGroup {
                out << """<div class="input-group">"""
                out << g.textField([
                    id: fieldId,
                    name: fieldName,
                    value: dateValue,
                    class: cssClasses.join(' '),
                    placeholder: placeholder,
                    readonly: readonly,
                    required: required,
                    'data-bs-toggle': 'datepicker',
                    'aria-describedby': helpText ? "${fieldId}-help" : null
                ] + filterHtmlAttributes(attrs))
                out << """<span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>"""
                out << """</div>"""

                if (hasFieldErrors(attrs)) {
                    out << renderFieldErrors(attrs)
                }
            }

            if (helpText) {
                out << renderHelpText(fieldId, helpText)
            }
        }
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    /**
     * Validate required attributes
     */
    private void validateRequiredAttributes(Map attrs, List<String> required) {
        required.each { attr ->
            if (!attrs[attr]) {
                throw new IllegalArgumentException("Attribute '${attr}' is required")
            }
        }
    }

    /**
     * Check if field has validation errors
     */
    private boolean hasFieldErrors(Map attrs) {
        if (!attrs.bean || !attrs.field) {
            return false
        }

        return attrs.bean.errors?.hasFieldErrors(attrs.field) ?: false
    }

    /**
     * Generate unique field ID
     */
    private String generateFieldId(String fieldName) {
        return "field_${fieldName}_${System.currentTimeMillis()}"
    }

    /**
     * Filter HTML attributes from tag attributes
     */
    private Map filterHtmlAttributes(Map attrs) {
        Map htmlAttrs = [:]

        // Standard HTML attributes to pass through
        ['title', 'style', 'data-*', 'aria-*', 'autocomplete', 'autofocus',
         'disabled', 'maxlength', 'minlength', 'pattern', 'step', 'min', 'max'].each { attr ->
            if (attrs[attr]) {
                htmlAttrs[attr] = attrs[attr]
            }
        }

        // Handle data and aria attributes
        attrs.each { key, value ->
            if (key.startsWith('data-') || key.startsWith('aria-')) {
                htmlAttrs[key] = value
            }
        }

        return htmlAttrs
    }

    /**
     * Render form group wrapper
     */
    private String renderFormGroup(Closure content) {
        return """<div class="mb-3">${content()}</div>"""
    }

    /**
     * Render input group wrapper
     */
    private String renderInputGroup(Closure content) {
        return """<div class="input-wrapper">${content()}</div>"""
    }

    /**
     * Render field label
     */
    private String renderLabel(String fieldId, String label, boolean required) {
        String requiredIndicator = required ? '<span class="text-danger ms-1">*</span>' : ''
        return """<label for="${fieldId}" class="form-label">${label}${requiredIndicator}</label>"""
    }

    /**
     * Render field errors
     */
    private String renderFieldErrors(Map attrs) {
        if (!hasFieldErrors(attrs)) {
            return ""
        }

        List<String> errors = attrs.bean.errors.getFieldErrors(attrs.field)*.defaultMessage
        String errorMessages = errors.join('<br>')

        return """<div class="invalid-feedback">${errorMessages}</div>"""
    }

    /**
     * Render help text
     */
    private String renderHelpText(String fieldId, String helpText) {
        return """<div id="${fieldId}-help" class="form-text">${helpText}</div>"""
    }
}
		