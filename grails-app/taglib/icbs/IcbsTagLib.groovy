package org.icbs

import java.text.Format
import java.text.SimpleDateFormat
import groovy.util.logging.Slf4j

/**
 * Enhanced ICBS TagLib for QwikBanka Core Banking System
 * Modern implementation with Bootstrap 5, security enhancements, and accessibility
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since Grails 6.2.3
 */
@Slf4j
class IcbsTagLib {

    static namespace = "icbs"

    /**
     * Enhanced date picker with modern Bootstrap 5 styling and validation
     * Usage: <icbs:datePicker name="birthDate" value="${customer.birthDate}" class="form-control"/>
     */
    def datePicker = { attrs, body ->
        String fieldName = attrs.name ?: 'date'
        String fieldId = attrs.id ?: "datepicker_${fieldName}_${System.currentTimeMillis()}"
        String cssClass = attrs.class ?: 'form-control'
        boolean required = attrs.required == 'true' || attrs.required == true
        boolean readonly = attrs.readonly == 'true' || attrs.readonly == true
        String placeholder = attrs.placeholder ?: 'MM/dd/yyyy'

        // Format date value
        String dateValue = ""
        def date = attrs.value
        if (date != null && date != "") {
            try {
                if (date instanceof Date) {
                    Format formatter = new SimpleDateFormat("MM/dd/yyyy")
                    dateValue = formatter.format(date)
                } else {
                    dateValue = date.toString()
                }
            } catch (Exception e) {
                log.warn("Error formatting date value: ${e.message}")
                dateValue = ""
            }
        }

        // Build enhanced date picker with modern styling
        out << """
        <div class="input-group date-picker-group" data-bs-toggle="tooltip" data-bs-placement="top" title="Select date">
            <input type="text"
                   id="${fieldId}"
                   name="${fieldName}"
                   value="${dateValue?.encodeAsHTML()}"
                   class="${cssClass} datepicker"
                   placeholder="${placeholder}"
                   ${required ? 'required' : ''}
                   ${readonly ? 'readonly' : ''}
                   data-bs-toggle="datepicker"
                   data-date-format="mm/dd/yyyy"
                   data-date-autoclose="true"
                   data-date-today-highlight="true"
                   autocomplete="off"
                   aria-label="Date input field">
            <span class="input-group-text">
                <i class="fas fa-calendar-alt" aria-hidden="true"></i>
            </span>
        </div>
        """
    }
    /**
     * Enhanced inline search modal with modern Bootstrap 5 and security improvements
     * Usage: <icbs:inlineSearch searchUrl="${createLink(controller:'search', action:'search')}" title="Customer Search"/>
     */
    def inlineSearch = { attrs, body ->
        String searchUrl = attrs.searchUrl ?: g.createLink(controller: 'search', action: 'search')
        String modalTitle = attrs.title ?: 'Search'
        String modalId = attrs.modalId ?: 'searchModal'
        String modalSize = attrs.size ?: 'modal-lg'

        out << """
        <!-- Enhanced Search Modal with Bootstrap 5 -->
        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
            <div class="modal-dialog ${modalSize} modal-dialog-centered modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="${modalId}Label">
                            <i class="fas fa-search me-2"></i>${modalTitle}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="searchDiv" class="search-content">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading search form...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Close
                        </button>
                        <button type="button" class="btn btn-primary" id="searchSubmitBtn" style="display:none;">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
        """

        // Enhanced JavaScript with modern fetch API and error handling
        out << """
        <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced search modal functionality
            window.openSearch = function() {
                const modal = new bootstrap.Modal(document.getElementById('${modalId}'));
                const searchDiv = document.getElementById('searchDiv');

                // Show loading state
                searchDiv.innerHTML = \`
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading search form...</p>
                    </div>
                \`;

                // Show modal
                modal.show();

                // Fetch search form with modern fetch API
                fetch('${searchUrl}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(\`HTTP error! status: \${response.status}\`);
                    }
                    return response.text();
                })
                .then(data => {
                    searchDiv.innerHTML = data;
                    document.getElementById('searchSubmitBtn').style.display = 'inline-block';

                    // Initialize any form components in the loaded content
                    initializeSearchForm();
                })
                .catch(error => {
                    console.error('Search form load error:', error);
                    searchDiv.innerHTML = \`
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Error loading search form:</strong> \${error.message}
                            <br><small>Please try again or contact support if the problem persists.</small>
                        </div>
                    \`;
                });

                return false;
            };

            // Initialize search form components
            function initializeSearchForm() {
                // Initialize date pickers in search form
                const datePickers = document.querySelectorAll('#searchDiv .datepicker');
                datePickers.forEach(picker => {
                    // Initialize date picker if library is available
                    if (typeof flatpickr !== 'undefined') {
                        flatpickr(picker, {
                            dateFormat: 'm/d/Y',
                            allowInput: true
                        });
                    }
                });

                // Focus on first input
                const firstInput = document.querySelector('#searchDiv input[type="text"]:not([readonly])');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 100);
                }
            }

            // Handle search form submission
            document.getElementById('searchSubmitBtn').addEventListener('click', function() {
                const searchForm = document.querySelector('#searchDiv form');
                if (searchForm) {
                    searchForm.submit();
                }
            });

            // Handle Enter key in search form
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && document.querySelector('#${modalId}:not(.d-none)')) {
                    const searchForm = document.querySelector('#searchDiv form');
                    if (searchForm && e.target.closest('#searchDiv')) {
                        e.preventDefault();
                        searchForm.submit();
                    }
                }
            });
        });
        </script>
        """
    }

    /**
     * Enhanced money format tag with currency support
     * Usage: <icbs:formatMoney amount="${1234.56}" currency="USD"/>
     */
    def formatMoney = { attrs, body ->
        def amount = attrs.amount
        String currency = attrs.currency ?: 'PHP'
        String locale = attrs.locale ?: 'en_US'

        if (amount == null) {
            out << '0.00'
            return
        }

        try {
            BigDecimal value = amount instanceof BigDecimal ? amount : new BigDecimal(amount.toString())
            String formatted = String.format("%,.2f", value)

            if (attrs.showCurrency == 'true') {
                out << "${currency} ${formatted}"
            } else {
                out << formatted
            }
        } catch (Exception e) {
            log.error("Error formatting money amount: ${e.message}")
            out << '0.00'
        }
    }

    /**
     * Enhanced account number format tag with masking
     * Usage: <icbs:formatAccountNumber number="${account.accountNumber}" mask="true"/>
     */
    def formatAccountNumber = { attrs, body ->
        String accountNumber = attrs.number?.toString()
        boolean mask = attrs.mask == 'true'

        if (!accountNumber) {
            out << 'N/A'
            return
        }

        if (mask && accountNumber.length() > 4) {
            String masked = '*' * (accountNumber.length() - 4) + accountNumber.substring(accountNumber.length() - 4)
            out << masked
        } else {
            out << accountNumber
        }
    }
}
