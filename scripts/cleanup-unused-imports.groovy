#!/usr/bin/env groovy

/**
 * QwikBanka Core Banking System - Unused Import Cleanup Script
 * 
 * This script scans all .groovy files and identifies/removes unused imports
 * to optimize compilation and reduce memory footprint.
 * 
 * Usage: groovy scripts/cleanup-unused-imports.groovy
 */

import java.util.regex.Pattern
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardOpenOption

class UnusedImportCleaner {
    
    static final List<String> DIRECTORIES_TO_SCAN = [
        'grails-app/controllers',
        'grails-app/services', 
        'grails-app/domain',
        'src/main/groovy',
        'src/test/groovy'
    ]
    
    static final List<String> ALWAYS_KEEP_IMPORTS = [
        'grails.converters.JSON',
        'grails.gorm.transactions.Transactional',
        'static org.springframework.http.HttpStatus.*'
    ]
    
    static final Pattern IMPORT_PATTERN = ~/^import\s+(.+)$/
    static final Pattern STATIC_IMPORT_PATTERN = ~/^import\s+static\s+(.+)$/
    
    def processedFiles = 0
    def totalImportsRemoved = 0
    def issuesFound = []
    
    static void main(String[] args) {
        def cleaner = new UnusedImportCleaner()
        cleaner.cleanupUnusedImports()
    }
    
    def cleanupUnusedImports() {
        println "🧹 QwikBanka Unused Import Cleanup Starting..."
        println "=" * 60
        
        DIRECTORIES_TO_SCAN.each { directory ->
            if (new File(directory).exists()) {
                println "📁 Scanning directory: ${directory}"
                scanDirectory(directory)
            } else {
                println "⚠️  Directory not found: ${directory}"
            }
        }
        
        printSummary()
    }
    
    def scanDirectory(String directoryPath) {
        def directory = new File(directoryPath)
        
        directory.eachFileRecurse { file ->
            if (file.name.endsWith('.groovy')) {
                processGroovyFile(file)
            }
        }
    }
    
    def processGroovyFile(File file) {
        try {
            def content = file.text
            def lines = content.split('\n')
            
            def imports = extractImports(lines)
            def usedImports = findUsedImports(content, imports)
            def unusedImports = imports.findAll { importLine ->
                !isImportUsed(importLine, content) && !isAlwaysKeepImport(importLine)
            }
            
            if (unusedImports.size() > 0) {
                removeUnusedImports(file, unusedImports)
                println "  ✅ ${file.path}: Removed ${unusedImports.size()} unused imports"
                totalImportsRemoved += unusedImports.size()
            }
            
            processedFiles++
            
        } catch (Exception e) {
            issuesFound << "❌ Error processing ${file.path}: ${e.message}"
        }
    }
    
    def extractImports(String[] lines) {
        def imports = []
        
        lines.each { line ->
            def trimmed = line.trim()
            if (trimmed.startsWith('import ')) {
                imports << trimmed
            }
        }
        
        return imports
    }
    
    def isImportUsed(String importLine, String content) {
        // Extract the class name from import
        def matcher = importLine =~ /import\s+(?:static\s+)?(.+?)(?:\.\*)?$/
        if (!matcher) return true // Keep if we can't parse
        
        def importPath = matcher[0][1]
        def className = importPath.split('\\.').last()
        
        // Handle static imports
        if (importLine.contains('static')) {
            return isStaticImportUsed(importPath, content)
        }
        
        // Handle wildcard imports
        if (importLine.contains('.*')) {
            return true // Keep wildcard imports for now
        }
        
        // Check if class name is used in the content
        return isClassNameUsed(className, content)
    }
    
    def isStaticImportUsed(String importPath, String content) {
        // For static imports, check if any method/field is used
        def parts = importPath.split('\\.')
        if (parts.length < 2) return true
        
        def methodOrField = parts.last()
        
        // Check for method calls or field references
        def patterns = [
            ~/${methodOrField}\s*\(/,  // Method call
            ~/${methodOrField}\s*=/,   // Assignment
            ~/\s${methodOrField}\s/,   // Field reference
            ~/\.${methodOrField}\b/    // Qualified reference
        ]
        
        return patterns.any { pattern ->
            content =~ pattern
        }
    }
    
    def isClassNameUsed(String className, String content) {
        // Remove import statements and comments from content for analysis
        def cleanContent = removeImportsAndComments(content)
        
        // Patterns to check for class usage
        def patterns = [
            ~/\b${className}\b\s*\(/,           // Constructor call
            ~/\b${className}\b\s*\./,           // Static method call
            ~/\b${className}\b\s+\w+/,          // Variable declaration
            ~/:\s*${className}\b/,               // Type annotation
            ~/\<${className}\>/,                 // Generic type
            ~/\(${className}\b/,                 // Parameter type
            ~/new\s+${className}\b/,            // New instance
            ~/instanceof\s+${className}\b/,     // instanceof check
            ~/as\s+${className}\b/,             // Type casting
            ~/\b${className}\.get\(/,           // Static get method
            ~/\b${className}\.find/,            // Static find method
            ~/\b${className}\.create/,          // Static create method
            ~/\b${className}\.count/,           // Static count method
            ~/\b${className}\.list/,            // Static list method
            ~/\b${className}\.save/,            // Static save method
            ~/\b${className}\.delete/           // Static delete method
        ]
        
        return patterns.any { pattern ->
            cleanContent =~ pattern
        }
    }
    
    def removeImportsAndComments(String content) {
        def lines = content.split('\n')
        def cleanLines = []
        def inBlockComment = false
        
        lines.each { line ->
            def trimmed = line.trim()
            
            // Skip import statements
            if (trimmed.startsWith('import ')) {
                return
            }
            
            // Handle block comments
            if (trimmed.contains('/*')) {
                inBlockComment = true
            }
            if (inBlockComment) {
                if (trimmed.contains('*/')) {
                    inBlockComment = false
                }
                return
            }
            
            // Skip single line comments
            if (trimmed.startsWith('//') || trimmed.startsWith('*')) {
                return
            }
            
            cleanLines << line
        }
        
        return cleanLines.join('\n')
    }
    
    def isAlwaysKeepImport(String importLine) {
        return ALWAYS_KEEP_IMPORTS.any { keepImport ->
            importLine.contains(keepImport)
        }
    }
    
    def removeUnusedImports(File file, List<String> unusedImports) {
        def content = file.text
        def lines = content.split('\n')
        def newLines = []
        
        lines.each { line ->
            def trimmed = line.trim()
            if (!unusedImports.contains(trimmed)) {
                newLines << line
            }
        }
        
        // Remove consecutive empty lines in import section
        def cleanedLines = removeConsecutiveEmptyLines(newLines)
        
        file.text = cleanedLines.join('\n')
    }
    
    def removeConsecutiveEmptyLines(List<String> lines) {
        def result = []
        def previousEmpty = false
        def inImportSection = true
        
        lines.each { line ->
            def trimmed = line.trim()
            
            // Check if we're still in import section
            if (inImportSection && !trimmed.startsWith('import ') && !trimmed.isEmpty() && !trimmed.startsWith('package')) {
                inImportSection = false
            }
            
            if (inImportSection && trimmed.isEmpty()) {
                if (!previousEmpty) {
                    result << line
                }
                previousEmpty = true
            } else {
                result << line
                previousEmpty = false
            }
        }
        
        return result
    }
    
    def printSummary() {
        println "=" * 60
        println "🎉 Cleanup Summary:"
        println "   📁 Files processed: ${processedFiles}"
        println "   🗑️  Total imports removed: ${totalImportsRemoved}"
        
        if (issuesFound.size() > 0) {
            println "   ⚠️  Issues found: ${issuesFound.size()}"
            issuesFound.each { issue ->
                println "      ${issue}"
            }
        }
        
        println "=" * 60
        println "✅ Unused import cleanup completed!"
        
        if (totalImportsRemoved > 0) {
            println "💡 Recommendation: Run your tests to ensure no functionality was broken."
        }
    }
}

// Run the cleanup
new UnusedImportCleaner().cleanupUnusedImports()
