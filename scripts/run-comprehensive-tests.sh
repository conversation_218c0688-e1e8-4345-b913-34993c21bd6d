#!/bin/bash

# QwikBanka Core Banking System - Comprehensive Test Execution Script
# This script runs all tests including unit tests, integration tests, and performance tests

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_RESULTS_DIR="${PROJECT_ROOT}/test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${TEST_RESULTS_DIR}/test_execution_${TIMESTAMP}.log"

# Create test results directory
mkdir -p "${TEST_RESULTS_DIR}"

# Logging function
log() {
    echo -e "${1}" | tee -a "${LOG_FILE}"
}

# Print header
print_header() {
    log "${BLUE}================================================================${NC}"
    log "${BLUE}🚀 QwikBanka Core Banking System - Comprehensive Test Suite${NC}"
    log "${BLUE}================================================================${NC}"
    log "${YELLOW}Started at: $(date)${NC}"
    log "${YELLOW}Project Root: ${PROJECT_ROOT}${NC}"
    log "${YELLOW}Log File: ${LOG_FILE}${NC}"
    log ""
}

# Print section header
print_section() {
    log ""
    log "${BLUE}📋 $1${NC}"
    log "${BLUE}$(printf '=%.0s' {1..60})${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_section "Checking Prerequisites"
    
    # Check if Grails is installed
    if ! command -v grails &> /dev/null; then
        log "${RED}❌ Grails is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Check if Java is installed
    if ! command -v java &> /dev/null; then
        log "${RED}❌ Java is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Check if database is running (PostgreSQL)
    if ! pg_isready -h localhost -p 5432 &> /dev/null; then
        log "${YELLOW}⚠️  PostgreSQL is not running on localhost:5432${NC}"
        log "${YELLOW}   Please ensure database is running before proceeding${NC}"
    fi
    
    log "${GREEN}✅ Prerequisites check completed${NC}"
}

# Clean previous test results
clean_previous_results() {
    print_section "Cleaning Previous Test Results"
    
    cd "${PROJECT_ROOT}"
    
    # Clean Grails cache
    if [ -d "build" ]; then
        rm -rf build
        log "${GREEN}✅ Cleaned build directory${NC}"
    fi
    
    # Clean test reports
    if [ -d "build/reports" ]; then
        rm -rf build/reports
        log "${GREEN}✅ Cleaned test reports${NC}"
    fi
    
    log "${GREEN}✅ Cleanup completed${NC}"
}

# Run unit tests
run_unit_tests() {
    print_section "Running Unit Tests"
    
    cd "${PROJECT_ROOT}"
    
    log "${YELLOW}🧪 Executing unit tests...${NC}"
    
    if grails test-app unit: --stacktrace 2>&1 | tee -a "${LOG_FILE}"; then
        log "${GREEN}✅ Unit tests completed successfully${NC}"
        
        # Copy test results
        if [ -d "build/reports/tests/test" ]; then
            cp -r build/reports/tests/test "${TEST_RESULTS_DIR}/unit-tests-${TIMESTAMP}"
            log "${GREEN}✅ Unit test results saved to ${TEST_RESULTS_DIR}/unit-tests-${TIMESTAMP}${NC}"
        fi
        
        return 0
    else
        log "${RED}❌ Unit tests failed${NC}"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    print_section "Running Integration Tests"
    
    cd "${PROJECT_ROOT}"
    
    log "${YELLOW}🔗 Executing integration tests...${NC}"
    
    if grails test-app integration: --stacktrace 2>&1 | tee -a "${LOG_FILE}"; then
        log "${GREEN}✅ Integration tests completed successfully${NC}"
        
        # Copy test results
        if [ -d "build/reports/tests/integrationTest" ]; then
            cp -r build/reports/tests/integrationTest "${TEST_RESULTS_DIR}/integration-tests-${TIMESTAMP}"
            log "${GREEN}✅ Integration test results saved to ${TEST_RESULTS_DIR}/integration-tests-${TIMESTAMP}${NC}"
        fi
        
        return 0
    else
        log "${RED}❌ Integration tests failed${NC}"
        return 1
    fi
}

# Run functional tests
run_functional_tests() {
    print_section "Running Functional Tests"
    
    cd "${PROJECT_ROOT}"
    
    log "${YELLOW}⚙️  Executing functional tests...${NC}"
    
    if grails test-app functional: --stacktrace 2>&1 | tee -a "${LOG_FILE}"; then
        log "${GREEN}✅ Functional tests completed successfully${NC}"
        
        # Copy test results
        if [ -d "build/reports/tests/functionalTest" ]; then
            cp -r build/reports/tests/functionalTest "${TEST_RESULTS_DIR}/functional-tests-${TIMESTAMP}"
            log "${GREEN}✅ Functional test results saved to ${TEST_RESULTS_DIR}/functional-tests-${TIMESTAMP}${NC}"
        fi
        
        return 0
    else
        log "${RED}❌ Functional tests failed${NC}"
        return 1
    fi
}

# Run performance tests
run_performance_tests() {
    print_section "Running Performance Tests"
    
    cd "${PROJECT_ROOT}"
    
    log "${YELLOW}🚀 Executing performance tests...${NC}"
    
    # Start application in test mode
    log "${YELLOW}📱 Starting application for performance testing...${NC}"
    grails run-app --environment=test &
    APP_PID=$!
    
    # Wait for application to start
    sleep 30
    
    # Check if application is running
    if ! curl -s http://localhost:8080/health > /dev/null; then
        log "${RED}❌ Application failed to start${NC}"
        kill $APP_PID 2>/dev/null || true
        return 1
    fi
    
    log "${GREEN}✅ Application started successfully${NC}"
    
    # Run performance tests (if JMeter is available)
    if command -v jmeter &> /dev/null; then
        log "${YELLOW}🏃 Running JMeter performance tests...${NC}"
        
        # Create simple performance test plan
        cat > "${TEST_RESULTS_DIR}/performance-test-${TIMESTAMP}.jmx" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="QwikBanka Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables"/>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Banking Operations">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControllerGui" testclass="LoopController" testname="Loop Controller">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">10</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">10</stringProp>
        <stringProp name="ThreadGroup.ramp_time">30</stringProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Health Check">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables"/>
          <stringProp name="HTTPSampler.domain">localhost</stringProp>
          <stringProp name="HTTPSampler.port">8080</stringProp>
          <stringProp name="HTTPSampler.path">/health</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
        </HTTPSamplerProxy>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
EOF
        
        # Run JMeter test
        jmeter -n -t "${TEST_RESULTS_DIR}/performance-test-${TIMESTAMP}.jmx" \
               -l "${TEST_RESULTS_DIR}/performance-results-${TIMESTAMP}.jtl" \
               -e -o "${TEST_RESULTS_DIR}/performance-report-${TIMESTAMP}" 2>&1 | tee -a "${LOG_FILE}"
        
        log "${GREEN}✅ Performance tests completed${NC}"
    else
        log "${YELLOW}⚠️  JMeter not found, skipping performance tests${NC}"
    fi
    
    # Stop application
    kill $APP_PID 2>/dev/null || true
    log "${GREEN}✅ Application stopped${NC}"
}

# Run code quality checks
run_code_quality_checks() {
    print_section "Running Code Quality Checks"
    
    cd "${PROJECT_ROOT}"
    
    log "${YELLOW}🔍 Running code quality analysis...${NC}"
    
    # Run CodeNarc (if available)
    if grails codenarc 2>&1 | tee -a "${LOG_FILE}"; then
        log "${GREEN}✅ CodeNarc analysis completed${NC}"
        
        # Copy results
        if [ -f "build/reports/codenarc/main.html" ]; then
            cp build/reports/codenarc/main.html "${TEST_RESULTS_DIR}/codenarc-report-${TIMESTAMP}.html"
            log "${GREEN}✅ CodeNarc report saved${NC}"
        fi
    else
        log "${YELLOW}⚠️  CodeNarc analysis failed or not available${NC}"
    fi
    
    # Run unused import cleanup
    if [ -f "scripts/cleanup-unused-imports.groovy" ]; then
        log "${YELLOW}🧹 Running unused import cleanup...${NC}"
        groovy scripts/cleanup-unused-imports.groovy 2>&1 | tee -a "${LOG_FILE}"
        log "${GREEN}✅ Import cleanup completed${NC}"
    fi
}

# Generate test summary
generate_test_summary() {
    print_section "Generating Test Summary"
    
    local summary_file="${TEST_RESULTS_DIR}/test-summary-${TIMESTAMP}.md"
    
    cat > "${summary_file}" << EOF
# QwikBanka Core Banking System - Test Execution Summary

**Execution Date**: $(date)
**Test Suite Version**: 1.0
**Environment**: Test

## Test Results Summary

| Test Type | Status | Details |
|-----------|--------|---------|
| Unit Tests | ${UNIT_TEST_STATUS:-❌ Not Run} | ${UNIT_TEST_DETAILS:-} |
| Integration Tests | ${INTEGRATION_TEST_STATUS:-❌ Not Run} | ${INTEGRATION_TEST_DETAILS:-} |
| Functional Tests | ${FUNCTIONAL_TEST_STATUS:-❌ Not Run} | ${FUNCTIONAL_TEST_DETAILS:-} |
| Performance Tests | ${PERFORMANCE_TEST_STATUS:-❌ Not Run} | ${PERFORMANCE_TEST_DETAILS:-} |
| Code Quality | ${CODE_QUALITY_STATUS:-❌ Not Run} | ${CODE_QUALITY_DETAILS:-} |

## Test Artifacts

- **Log File**: test_execution_${TIMESTAMP}.log
- **Unit Test Results**: unit-tests-${TIMESTAMP}/
- **Integration Test Results**: integration-tests-${TIMESTAMP}/
- **Functional Test Results**: functional-tests-${TIMESTAMP}/
- **Performance Test Results**: performance-report-${TIMESTAMP}/
- **Code Quality Report**: codenarc-report-${TIMESTAMP}.html

## Recommendations

1. Review any failed tests and address issues
2. Ensure all critical functionality is covered
3. Monitor performance metrics for production readiness
4. Address any code quality issues identified

## Next Steps

- [ ] Review test results
- [ ] Fix any identified issues
- [ ] Re-run failed tests
- [ ] Prepare for UAT
- [ ] Plan production deployment

---
Generated by QwikBanka Test Automation Suite
EOF

    log "${GREEN}✅ Test summary generated: ${summary_file}${NC}"
}

# Main execution function
main() {
    print_header
    
    # Initialize status variables
    UNIT_TEST_STATUS="❌ Not Run"
    INTEGRATION_TEST_STATUS="❌ Not Run"
    FUNCTIONAL_TEST_STATUS="❌ Not Run"
    PERFORMANCE_TEST_STATUS="❌ Not Run"
    CODE_QUALITY_STATUS="❌ Not Run"
    
    # Check prerequisites
    check_prerequisites
    
    # Clean previous results
    clean_previous_results
    
    # Run tests
    if run_unit_tests; then
        UNIT_TEST_STATUS="✅ Passed"
    else
        UNIT_TEST_STATUS="❌ Failed"
    fi
    
    if run_integration_tests; then
        INTEGRATION_TEST_STATUS="✅ Passed"
    else
        INTEGRATION_TEST_STATUS="❌ Failed"
    fi
    
    if run_functional_tests; then
        FUNCTIONAL_TEST_STATUS="✅ Passed"
    else
        FUNCTIONAL_TEST_STATUS="❌ Failed"
    fi
    
    if run_performance_tests; then
        PERFORMANCE_TEST_STATUS="✅ Passed"
    else
        PERFORMANCE_TEST_STATUS="❌ Failed"
    fi
    
    if run_code_quality_checks; then
        CODE_QUALITY_STATUS="✅ Passed"
    else
        CODE_QUALITY_STATUS="❌ Failed"
    fi
    
    # Generate summary
    generate_test_summary
    
    # Final status
    print_section "Test Execution Complete"
    log "${GREEN}🎉 All tests completed!${NC}"
    log "${YELLOW}📊 Check test results in: ${TEST_RESULTS_DIR}${NC}"
    log "${YELLOW}📋 Test summary: test-summary-${TIMESTAMP}.md${NC}"
    log "${YELLOW}📝 Full log: test_execution_${TIMESTAMP}.log${NC}"
    log ""
    log "${BLUE}================================================================${NC}"
    log "${BLUE}✅ QwikBanka Test Suite Execution Complete${NC}"
    log "${BLUE}================================================================${NC}"
}

# Run main function
main "$@"
