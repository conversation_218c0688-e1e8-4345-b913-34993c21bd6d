package org.icbs.deposit

import grails.testing.mixin.integration.Integration
import grails.gorm.transactions.Rollback
import spock.lang.Specification
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus

/**
 * Integration tests for DepositAccountController
 * Tests all deposit account management operations
 */
@Integration
@Rollback
class DepositAccountControllerIntegrationSpec extends Specification {

    DepositAccountController controller
    def sessionFactory

    def setup() {
        controller = new DepositAccountController()
        setupTestData()
    }

    def cleanup() {
        sessionFactory.currentSession.flush()
        sessionFactory.currentSession.clear()
    }

    void "test deposit account creation"() {
        given: "Valid deposit parameters"
        def customer = Customer.findByDisplayName("John Doe")
        
        controller.params.customerId = customer.id
        controller.params.depositType = "SAVINGS"
        controller.params.initialDeposit = 5000
        controller.params.interestRate = 2.5

        when: "Deposit account is created"
        controller.save()

        then: "Deposit is successfully created"
        controller.response.redirectedUrl.contains("show")
        Deposit.countByCustomer(customer) > 0
    }

    void "test deposit account validation"() {
        given: "Invalid deposit parameters"
        controller.params.initialDeposit = -1000 // Invalid amount
        controller.params.interestRate = -5 // Invalid rate

        when: "Deposit account creation is attempted"
        controller.save()

        then: "Validation errors are returned"
        controller.response.status == 200 // Stays on form
        controller.modelAndView.model.depositInstance.hasErrors()
    }

    void "test deposit account inquiry"() {
        given: "An existing deposit"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Deposit inquiry is performed"
        controller.params.accountNo = deposit.accountNo
        controller.inquireDeposit()

        then: "Deposit details are returned"
        controller.response.json.found == true
        controller.response.json.accountNo == deposit.accountNo
        controller.response.json.customerName != null
        controller.response.json.ledgerBalance != null
        controller.response.json.availableBalance != null
    }

    void "test deposit account balance inquiry"() {
        given: "An existing deposit"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Balance inquiry is performed"
        controller.params.accountNo = deposit.accountNo
        controller.getBalance()

        then: "Balance information is returned"
        controller.response.json.ledgerBalance == deposit.ledgerBalAmt
        controller.response.json.availableBalance == deposit.availableBalAmt
        controller.response.json.currency != null
    }

    void "test deposit account transaction history"() {
        given: "An existing deposit"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Transaction history is requested"
        controller.params.accountNo = deposit.accountNo
        controller.params.fromDate = "01/01/2024"
        controller.params.toDate = "31/12/2024"
        controller.getTransactionHistory()

        then: "Transaction history is returned"
        controller.response.json.containsKey('transactions')
        controller.response.json.containsKey('totalCount')
        controller.response.json.containsKey('summary')
    }

    void "test deposit account status change"() {
        given: "An active deposit"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Deposit status is changed"
        controller.params.id = deposit.id
        controller.params.newStatus = DepositStatus.get(3).id // Dormant
        controller.params.reason = "No activity for 12 months"
        controller.changeStatus()

        then: "Status is successfully changed"
        controller.response.json.success == true
        deposit.refresh()
        deposit.status.id == 3
    }

    void "test deposit account closure validation"() {
        given: "A deposit with balance"
        def deposit = Deposit.findByAccountNo("DEP001")
        deposit.ledgerBalAmt = 10000
        deposit.save(flush: true)

        when: "Closure is attempted"
        controller.params.id = deposit.id
        controller.validateClosure()

        then: "Closure validation is returned"
        controller.response.json.canClose == false
        controller.response.json.reason.contains("balance")
    }

    void "test deposit account interest calculation"() {
        given: "A deposit account"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Interest calculation is requested"
        controller.params.id = deposit.id
        controller.params.calculationDate = new Date().format("MM/dd/yyyy")
        controller.calculateInterest()

        then: "Interest calculation is returned"
        controller.response.json.containsKey('interestAmount')
        controller.response.json.containsKey('calculationDetails')
        controller.response.json.containsKey('effectiveRate')
    }

    void "test deposit account search functionality"() {
        when: "Deposit search is performed"
        controller.params.query = "John"
        controller.search()

        then: "Search results are returned"
        controller.response.json.size() > 0
        controller.response.json[0].customerName.contains("John")
    }

    void "test deposit account listing with filters"() {
        when: "Filtered listing is requested"
        controller.params.status = "ACTIVE"
        controller.params.type = "SAVINGS"
        controller.params.max = 10
        def model = controller.index()

        then: "Filtered results are returned"
        model.depositInstanceList != null
        model.depositInstanceList.size() <= 10
        model.depositInstanceCount != null
    }

    void "test deposit account statement generation"() {
        given: "A deposit account"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Statement generation is requested"
        controller.params.accountNo = deposit.accountNo
        controller.params.fromDate = "01/01/2024"
        controller.params.toDate = "31/01/2024"
        controller.params.format = "PDF"
        controller.generateStatement()

        then: "Statement is generated"
        controller.response.json.success == true
        controller.response.json.statementId != null
        controller.response.json.downloadUrl != null
    }

    void "test deposit account maintenance operations"() {
        given: "A deposit account"
        def deposit = Deposit.findByAccountNo("DEP001")

        when: "Maintenance operation is performed"
        controller.params.id = deposit.id
        controller.params.operation = "UPDATE_INTEREST_RATE"
        controller.params.newRate = 3.0
        controller.params.effectiveDate = new Date().format("MM/dd/yyyy")
        controller.performMaintenance()

        then: "Maintenance is completed"
        controller.response.json.success == true
        controller.response.json.message.contains("updated")
    }

    // Helper methods
    private void setupTestData() {
        // Create test branch
        def branch = new Branch(
            name: "Test Branch",
            code: "TB001",
            runDate: new Date()
        )
        branch.save(flush: true, failOnError: true)

        // Create test user
        def user = new UserMaster(
            username: "test_deposit_officer",
            firstName: "Test",
            lastName: "Officer",
            branch: branch
        )
        user.save(flush: true, failOnError: true)

        // Create test customer
        def customer = new Customer(
            firstName: "John",
            lastName: "Doe",
            displayName: "John Doe",
            branch: branch
        )
        customer.save(flush: true, failOnError: true)

        // Create test deposit
        def deposit = new Deposit(
            accountNo: "DEP001",
            customer: customer,
            branch: branch,
            ledgerBalAmt: 25000,
            availableBalAmt: 25000,
            interestRate: 2.5,
            dateOpened: new Date() - 90,
            status: DepositStatus.get(4) // Active
        )
        deposit.save(flush: true, failOnError: true)
    }
}
