package org.icbs.loans

import grails.testing.mixin.integration.Integration
import grails.gorm.transactions.Rollback
import spock.lang.Specification
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Product
import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.LoanAcctStatus

/**
 * Integration tests for LoanAccountController
 * Tests all loan account management operations
 */
@Integration
@Rollback
class LoanAccountControllerIntegrationSpec extends Specification {

    LoanAccountController controller
    def sessionFactory

    def setup() {
        controller = new LoanAccountController()
        setupTestData()
    }

    def cleanup() {
        sessionFactory.currentSession.flush()
        sessionFactory.currentSession.clear()
    }

    void "test loan account creation"() {
        given: "Valid loan parameters"
        def customer = Customer.findByDisplayName("John Doe")
        def product = Product.findByName("Personal Loan")
        
        controller.params.customerId = customer.id
        controller.params.productId = product.id
        controller.params.loanAmount = 100000
        controller.params.interestRate = 12.5
        controller.params.termInMonths = 24

        when: "Loan account is created"
        controller.save()

        then: "Loan is successfully created"
        controller.response.redirectedUrl.contains("show")
        Loan.countByCustomer(customer) > 0
    }

    void "test loan account validation"() {
        given: "Invalid loan parameters"
        controller.params.loanAmount = -1000 // Invalid amount
        controller.params.interestRate = 50 // Exceeds maximum
        controller.params.termInMonths = 0 // Invalid term

        when: "Loan account creation is attempted"
        controller.save()

        then: "Validation errors are returned"
        controller.response.status == 200 // Stays on form
        controller.modelAndView.model.loanInstance.hasErrors()
    }

    void "test loan account inquiry"() {
        given: "An existing loan"
        def loan = Loan.findByAccountNo("LN001")

        when: "Loan inquiry is performed"
        controller.params.accountNo = loan.accountNo
        controller.inquireLoan()

        then: "Loan details are returned"
        controller.response.json.found == true
        controller.response.json.accountNo == loan.accountNo
        controller.response.json.customerName != null
        controller.response.json.balanceAmount != null
    }

    void "test loan account update"() {
        given: "An existing loan"
        def loan = Loan.findByAccountNo("LN001")
        def originalRate = loan.interestRate

        when: "Loan is updated"
        controller.params.id = loan.id
        controller.params.interestRate = originalRate + 1
        controller.params.remarks = "Rate adjustment"
        controller.update()

        then: "Loan is successfully updated"
        loan.refresh()
        loan.interestRate == originalRate + 1
    }

    void "test loan account status change"() {
        given: "An active loan"
        def loan = Loan.findByAccountNo("LN001")

        when: "Loan status is changed"
        controller.params.id = loan.id
        controller.params.newStatus = LoanAcctStatus.get(3).id // Suspended
        controller.params.reason = "Customer request"
        controller.changeStatus()

        then: "Status is successfully changed"
        controller.response.json.success == true
        loan.refresh()
        loan.status.id == 3
    }

    void "test loan account search"() {
        when: "Loan search is performed"
        controller.params.query = "John"
        controller.search()

        then: "Search results are returned"
        controller.response.json.size() > 0
        controller.response.json[0].customerName.contains("John")
    }

    void "test loan account listing with pagination"() {
        when: "Loan listing is requested"
        controller.params.max = 10
        controller.params.offset = 0
        def model = controller.index()

        then: "Paginated results are returned"
        model.loanInstanceList != null
        model.loanInstanceList.size() <= 10
        model.loanInstanceCount != null
    }

    void "test loan account deletion prevention"() {
        given: "A loan with outstanding balance"
        def loan = Loan.findByAccountNo("LN001")
        loan.balanceAmount = 50000
        loan.save(flush: true)

        when: "Deletion is attempted"
        controller.params.id = loan.id
        controller.delete()

        then: "Deletion is prevented"
        controller.response.json.success == false
        controller.response.json.message.contains("outstanding balance")
        Loan.get(loan.id) != null
    }

    void "test loan account history tracking"() {
        given: "A loan account"
        def loan = Loan.findByAccountNo("LN001")

        when: "History is requested"
        controller.params.id = loan.id
        controller.getAccountHistory()

        then: "History records are returned"
        controller.response.json.size() >= 0
        // Should include creation, modifications, transactions
    }

    void "test loan account document management"() {
        given: "A loan account"
        def loan = Loan.findByAccountNo("LN001")

        when: "Documents are requested"
        controller.params.id = loan.id
        controller.getDocuments()

        then: "Document list is returned"
        controller.response.json.containsKey('documents')
        controller.response.json.containsKey('documentCount')
    }

    void "test loan account performance metrics"() {
        given: "A loan account"
        def loan = Loan.findByAccountNo("LN001")

        when: "Performance metrics are requested"
        controller.params.id = loan.id
        controller.getPerformanceMetrics()

        then: "Metrics are returned"
        controller.response.json.containsKey('classification')
        controller.response.json.containsKey('daysPastDue')
        controller.response.json.containsKey('paymentHistory')
    }

    // Helper methods
    private void setupTestData() {
        // Create test branch
        def branch = new Branch(
            name: "Test Branch",
            code: "TB001",
            runDate: new Date()
        )
        branch.save(flush: true, failOnError: true)

        // Create test user
        def user = new UserMaster(
            username: "test_loan_officer",
            firstName: "Test",
            lastName: "Officer",
            branch: branch
        )
        user.save(flush: true, failOnError: true)

        // Create test customer
        def customer = new Customer(
            firstName: "John",
            lastName: "Doe",
            displayName: "John Doe",
            branch: branch
        )
        customer.save(flush: true, failOnError: true)

        // Create test product
        def product = new Product(
            name: "Personal Loan",
            code: "PL001",
            minAmount: 10000,
            maxAmount: 500000,
            minInterestRate: 8.0,
            maxInterestRate: 18.0,
            status: ConfigItemStatus.get(2)
        )
        product.save(flush: true, failOnError: true)

        // Create test loan
        def loan = new Loan(
            accountNo: "LN001",
            customer: customer,
            product: product,
            branch: branch,
            loanAmount: 100000,
            balanceAmount: 75000,
            interestRate: 12.5,
            termInMonths: 24,
            dateReleased: new Date() - 180,
            maturityDate: new Date() + 180,
            status: LoanAcctStatus.get(4) // Active
        )
        loan.save(flush: true, failOnError: true)
    }
}
