package org.icbs.tellering

import grails.testing.mixin.integration.Integration
import grails.gorm.transactions.Rollback
import spock.lang.Specification
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Institution
import org.icbs.lov.ConfigItemStatus
import org.icbs.deposit.Deposit
import org.icbs.loans.Loan
import org.icbs.cif.Customer

/**
 * Integration tests for TellerCoreController
 * Tests all core teller operations and functionality
 */
@Integration
@Rollback
class TellerCoreControllerIntegrationSpec extends Specification {

    TellerCoreController controller
    def sessionFactory

    def setup() {
        controller = new TellerCoreController()
        
        // Create test data
        setupTestData()
    }

    def cleanup() {
        // Clean up session
        sessionFactory.currentSession.flush()
        sessionFactory.currentSession.clear()
    }

    void "test index action displays teller dashboard"() {
        given: "A logged in teller user"
        def user = UserMaster.findByUsername("test_teller")
        controller.session.user_id = user.id

        when: "Index action is called"
        def model = controller.index()

        then: "Dashboard data is returned"
        model != null
        model.containsKey('user')
        model.containsKey('branch')
        model.containsKey('runDate')
    }

    void "test account lookup functionality"() {
        given: "A valid account number"
        def deposit = Deposit.findByAccountNo("DEP001")
        
        when: "Account lookup is performed"
        controller.params.accountNo = deposit.accountNo
        controller.params.accountType = "deposit"
        controller.lookupAccount()

        then: "Account details are returned"
        controller.response.json.found == true
        controller.response.json.accountNo == deposit.accountNo
        controller.response.json.customerName != null
    }

    void "test invalid account lookup"() {
        when: "Invalid account lookup is performed"
        controller.params.accountNo = "INVALID123"
        controller.params.accountType = "deposit"
        controller.lookupAccount()

        then: "Error response is returned"
        controller.response.json.found == false
        controller.response.json.message != null
    }

    void "test transaction success page"() {
        given: "A completed transaction"
        def txnFile = createTestTransaction()
        controller.session.transactionFileId = txnFile.id

        when: "Transaction success page is accessed"
        def model = controller.txnSuccess()

        then: "Transaction details are displayed"
        model != null
        model.txnFile != null
        model.txnFile.id == txnFile.id
    }

    void "test teller balance validation"() {
        given: "A teller user"
        def user = UserMaster.findByUsername("test_teller")
        controller.session.user_id = user.id

        when: "Balance validation is performed"
        controller.validateTellerBalance()

        then: "Balance status is returned"
        controller.response.json.containsKey('balanced')
        controller.response.json.containsKey('cashBalance')
        controller.response.json.containsKey('checkBalance')
    }

    void "test policy exception handling"() {
        given: "Policy exception parameters"
        controller.params.exceptionType = "AMOUNT_LIMIT"
        controller.params.amount = "150000"
        controller.params.reason = "Large withdrawal for business operations"

        when: "Policy exception is created"
        controller.createPolicyException()

        then: "Exception is properly logged"
        controller.response.json.success == true
        controller.response.json.exceptionId != null
    }

    void "test session management"() {
        given: "Session data"
        controller.session.testData = "test_value"
        controller.session.transactionFileId = 123

        when: "Session is cleared"
        controller.params.sessionKeys = "testData,transactionFileId"
        controller.clearSession()

        then: "Session data is removed"
        controller.response.json.success == true
        controller.session.testData == null
        controller.session.transactionFileId == null
    }

    void "test system status retrieval"() {
        when: "System status is requested"
        controller.getSystemStatus()

        then: "System status is returned"
        controller.response.json.containsKey('branchName')
        controller.response.json.containsKey('runDate')
        controller.response.json.containsKey('systemStatus')
        controller.response.json.systemStatus == "Online"
    }

    void "test transaction limits validation"() {
        given: "Transaction parameters"
        controller.params.amount = "50000"
        controller.params.txnType = "WITHDRAWAL"

        when: "Transaction limits are validated"
        controller.validateTransactionLimits()

        then: "Validation result is returned"
        controller.response.json.containsKey('valid')
        controller.response.json.containsKey('warnings')
        controller.response.json.containsKey('errors')
    }

    void "test emergency procedures"() {
        given: "Emergency situation"
        controller.params.emergencyType = "SYSTEM_LOCK"
        controller.params.reason = "Security breach detected"

        when: "Emergency procedure is triggered"
        controller.handleEmergency()

        then: "Emergency is properly handled"
        controller.response.json.success == true
        controller.response.json.emergencyId != null
    }

    void "test audit logging integration"() {
        given: "A teller user"
        def user = UserMaster.findByUsername("test_teller")
        controller.session.user_id = user.id

        when: "An auditable action is performed"
        controller.params.action = "ACCOUNT_INQUIRY"
        controller.params.details = "Customer account lookup"
        controller.logUserActivity()

        then: "Audit log is created"
        controller.response.json.success == true
        controller.response.json.message == "Activity logged"
    }

    // Helper methods
    private void setupTestData() {
        // Create test branch
        def branch = new Branch(
            name: "Test Branch",
            code: "TB001",
            runDate: new Date(),
            isEOD: false,
            isTelleringActive: true
        )
        branch.save(flush: true, failOnError: true)

        // Create test user
        def user = new UserMaster(
            username: "test_teller",
            firstName: "Test",
            lastName: "Teller",
            branch: branch,
            isTellerBalanced: true,
            singleTransactionLimit: 100000,
            dailyTransactionLimit: 500000
        )
        user.save(flush: true, failOnError: true)

        // Create test customer
        def customer = new Customer(
            firstName: "John",
            lastName: "Doe",
            displayName: "John Doe",
            branch: branch
        )
        customer.save(flush: true, failOnError: true)

        // Create test deposit account
        def deposit = new Deposit(
            accountNo: "DEP001",
            customer: customer,
            branch: branch,
            ledgerBalAmt: 50000,
            availableBalAmt: 50000,
            status: ConfigItemStatus.get(2)
        )
        deposit.save(flush: true, failOnError: true)
    }

    private def createTestTransaction() {
        def branch = Branch.findByCode("TB001")
        def user = UserMaster.findByUsername("test_teller")
        
        def txnFile = new TxnFile(
            txnCode: "TEST",
            txnDescription: "Test Transaction",
            txnDate: new Date(),
            txnAmt: 1000,
            status: ConfigItemStatus.get(2),
            branch: branch,
            user: user,
            txnTimestamp: new Date().toTimestamp()
        )
        txnFile.save(flush: true, failOnError: true)
        
        return txnFile
    }
}
