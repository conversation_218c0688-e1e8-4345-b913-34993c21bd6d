package org.icbs.deposit

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Institution
import org.icbs.admin.TxnTemplate
import org.icbs.deposit.Deposit
import org.icbs.deposit.TxnDepositAcctLedger
import org.icbs.deposit.TxnDepositFundTransfer
import org.icbs.tellering.TxnFile
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.TxnType

/**
 * REFACTORED: DepositTransactionService
 * Extracted from DepositService.groovy (2,035 lines)
 * Handles deposit transaction operations with modern patterns
 * 
 * This service manages:
 * - Deposit transactions
 * - Withdrawal transactions
 * - Fund transfers between accounts
 * - Transaction validation and processing
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III (Service Decomposition)
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class DepositTransactionService {
    
    // Service Dependencies
    def glTransactionService
    def auditLogService
    
    /**
     * Process deposit transaction
     */
    def processDeposit(Map params) {
        log.info("Processing deposit transaction for account: ${params.acctNo}")
        
        try {
            def result = [:]
            result.acct = Deposit.findByAcctNo(params.acctNo)
            
            if (!result.acct) {
                return [error: "Account not found"]
            }
            
            double amount = params.amt.toDouble()
            
            // Update account balances
            result.acct.ledgerBalAmt += amount
            result.acct.availableBalAmt += amount
            result.acct.interestBalAmt += amount
            
            def bal = result.acct.ledgerBalAmt
            
            // Create transaction file
            def txnFile1 = new TxnFile(
                txnDate: result.acct.branch.runDate,
                txnParticulars: params.txnDescription,
                txnTemplate: params.txnTemplate.id,
                txnCode: params.txnTemplate.code,
                txnDescription: params.txnTemplate.codeDescription,
                txnType: params.txnTemplate.txnType.id,
                currency: result.acct.product.currency.id,
                acctStatus: result.acct.status.id,
                status: ConfigItemStatus.read(2),
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(params.user),
                branch: result.acct.branch.id,
                acctNo: result.acct.acctNo,
                txnAmt: amount,
                txnRef: params.txnRef,
                depAcct: result.acct
            )
            
            // Create account ledger entry
            def acctLedger1 = new TxnDepositAcctLedger(
                txnType: TxnType.read(9),
                user: params.user,
                branch: params.branch,
                currency: result.acct.product.currency.id,
                status: org.icbs.lov.DepositStatus.read(2),
                txnDate: result.acct.branch.runDate,
                txnFile: txnFile1,
                acct: result.acct,
                acctNo: result.acct.acctNo,
                creditAmt: amount,
                bal: bal,
                txnRef: params.txnRef
            )
            
            // Save all entities
            if (!result.acct.save() || !txnFile1.save(flush: true, validate: false) || 
                !acctLedger1.save(validate: false)) {
                return [error: "Transaction save failed"]
            }
            
            // Process GL entries
            glTransactionService.saveTxnBreakdown(txnFile1.id)
            
            result.txnFile = txnFile1
            
            // Audit logging
            auditLogService.insert('080', 'DEP03001', 
                "Deposit transaction - Amount: ${amount}", 
                'DepositTransactionService', null, null, null, result.acct.id)
            
            log.info("Deposit transaction processed successfully")
            return result
            
        } catch (Exception e) {
            log.error("Error processing deposit transaction", e)
            return [error: "Transaction processing failed: ${e.message}"]
        }
    }
    
    /**
     * Process withdrawal transaction
     */
    def processWithdrawal(Map params) {
        log.info("Processing withdrawal transaction for account: ${params.acctNo}")
        
        try {
            def result = [:]
            result.acct = Deposit.findByAcctNo(params.acctNo)
            
            if (!result.acct) {
                return [error: "Account not found"]
            }
            
            double amount = params.amt.toDouble()
            
            // Validate sufficient balance
            if (result.acct.availableBalAmt < amount) {
                return [error: "Insufficient balance"]
            }
            
            // Update account balances
            result.acct.ledgerBalAmt -= amount
            result.acct.availableBalAmt -= amount
            result.acct.interestBalAmt -= amount
            
            def bal = result.acct.ledgerBalAmt
            
            // Create transaction file
            def txnFile1 = new TxnFile(
                txnDate: result.acct.branch.runDate,
                txnParticulars: params.txnDescription,
                txnTemplate: params.txnTemplate.id,
                txnCode: params.txnTemplate.code,
                txnDescription: params.txnTemplate.codeDescription,
                txnType: params.txnTemplate.txnType.id,
                currency: result.acct.product.currency.id,
                acctStatus: result.acct.status.id,
                status: ConfigItemStatus.read(2),
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(params.user),
                branch: result.acct.branch.id,
                acctNo: result.acct.acctNo,
                txnAmt: amount,
                txnRef: params.txnRef,
                depAcct: result.acct
            )
            
            // Create account ledger entry
            def acctLedger1 = new TxnDepositAcctLedger(
                txnType: TxnType.read(10), // Withdrawal
                user: params.user,
                branch: params.branch,
                currency: result.acct.product.currency.id,
                status: org.icbs.lov.DepositStatus.read(2),
                txnDate: result.acct.branch.runDate,
                txnFile: txnFile1,
                acct: result.acct,
                acctNo: result.acct.acctNo,
                debitAmt: amount,
                bal: bal,
                txnRef: params.txnRef
            )
            
            // Save all entities
            if (!result.acct.save() || !txnFile1.save(flush: true, validate: false) || 
                !acctLedger1.save(validate: false)) {
                return [error: "Transaction save failed"]
            }
            
            // Process GL entries
            glTransactionService.saveTxnBreakdown(txnFile1.id)
            
            result.txnFile = txnFile1
            
            // Audit logging
            auditLogService.insert('080', 'DEP03002', 
                "Withdrawal transaction - Amount: ${amount}", 
                'DepositTransactionService', null, null, null, result.acct.id)
            
            log.info("Withdrawal transaction processed successfully")
            return result
            
        } catch (Exception e) {
            log.error("Error processing withdrawal transaction", e)
            return [error: "Transaction processing failed: ${e.message}"]
        }
    }
    
    /**
     * Process fund transfer between accounts
     */
    def fundTransfer(Map params, long userId) {
        log.info("Processing fund transfer from ${params.fundingAcct.id} to ${params.destinationAcct.id}")
        
        try {
            def result = [:]
            result.fundingAcct = Deposit.get(params.fundingAcct.id)
            result.destinationAcct = Deposit.get(params.destinationAcct.id)
            result.txnTemplate = TxnTemplate.get(params.txnTemplate.id)
            
            // Validation checks
            if (params.fundingAcct == params.destinationAcct) { 
                return [error: "Cannot transfer to same account"]
            }
            
            if ((result.destinationAcct.statusId == 7) || (result.destinationAcct.statusId == 6)) { 
                return [error: "Destination account is closed or cancelled"]
            }
            
            if (result.fundingAcct.product.productType.id == 3 || result.destinationAcct.product.productType.id == 3) {
                return [error: "Fixed deposit accounts not allowed for transfer"]
            } 
            
            if (result.fundingAcct.product.currency != result.destinationAcct.product.currency) {
                return [error: "Currency mismatch between accounts"]
            }
            
            // Interbranch validation
            def txnTmp = TxnTemplate.get(params.txnTemplate.id)
            if (result.fundingAcct.branch != result.destinationAcct.branch) {
                if (txnTmp.interbranchTxn.id != 1) {
                    return [error: "Invalid interbranch transaction template"]
                }         
            } else {
                if (txnTmp.interbranchTxn.id == 1) {
                    return [error: "Interbranch template not allowed for same branch"]
                }              
            } 
            
            double amount = params.amt.toDouble()
            
            // Validate sufficient balance
            if (result.fundingAcct.availableBalAmt < amount) {
                return [error: "Insufficient balance in funding account"]
            }
            
            // Update account balances
            // Debit funding account
            result.fundingAcct.ledgerBalAmt -= amount
            result.fundingAcct.availableBalAmt -= amount
            result.fundingAcct.interestBalAmt -= amount
            
            // Credit destination account
            result.destinationAcct.ledgerBalAmt += amount
            result.destinationAcct.availableBalAmt += amount
            result.destinationAcct.interestBalAmt += amount
            
            // Get transaction templates
            def fundDr = TxnTemplate.get(Institution.findByParamCode('DEP.40121').paramValue.toInteger())
            def fundCr = TxnTemplate.get(Institution.findByParamCode('DEP.40122').paramValue.toInteger())
            
            // Create debit transaction file
            def txnFile1 = new TxnFile(
                txnDate: result.fundingAcct.branch.runDate,
                txnParticulars: params.txnDescription,
                txnTemplate: fundDr.id,
                txnCode: fundDr.code,
                txnDescription: fundDr.codeDescription,
                txnType: fundDr.txnType.id,
                currency: result.fundingAcct.product.currency.id,
                acctStatus: result.fundingAcct.status.id,
                status: ConfigItemStatus.read(2),
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(userId),
                branch: result.fundingAcct.branch.id,
                acctNo: result.fundingAcct.acctNo,
                txnAmt: amount,
                txnRef: params.txnRef,
                depAcct: result.fundingAcct
            )
            
            // Create credit transaction file
            def txnFile2 = new TxnFile(
                txnDate: result.fundingAcct.branch.runDate,
                txnParticulars: params.txnDescription,
                txnTemplate: fundCr.id,
                txnCode: fundCr.code,
                txnDescription: fundCr.codeDescription,
                txnType: fundCr.txnType.id,
                currency: result.destinationAcct.product.currency.id,
                acctStatus: result.destinationAcct.status.id,
                status: ConfigItemStatus.read(2),
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(userId),
                branch: result.destinationAcct.branch.id,
                acctNo: result.destinationAcct.acctNo,
                txnAmt: amount,
                txnRef: params.txnRef,
                depAcct: result.destinationAcct
            )
            
            // Create account ledger entries
            def acctLedger1 = new TxnDepositAcctLedger(
                txnType: fundDr.txnType.id,
                user: UserMaster.get(userId),
                branch: result.fundingAcct.branch,
                currency: result.fundingAcct.product.currency.id,
                status: ConfigItemStatus.read(2),
                txnDate: result.fundingAcct.branch.runDate,
                txnFile: txnFile1,
                acct: result.fundingAcct,
                acctNo: result.fundingAcct.acctNo,
                txnRef: params.txnRef,
                debitAmt: amount,
                bal: result.fundingAcct.ledgerBalAmt
            )
            
            def acctLedger2 = new TxnDepositAcctLedger(
                txnType: fundCr.txnType.id,
                user: UserMaster.get(userId),
                branch: result.destinationAcct.branch,
                currency: result.destinationAcct.product.currency.id,
                status: ConfigItemStatus.read(2),
                txnDate: result.fundingAcct.branch.runDate,
                txnFile: txnFile2,
                acct: result.destinationAcct,
                txnRef: params.txnRef,
                acctNo: result.destinationAcct.acctNo,
                creditAmt: amount,
                bal: result.destinationAcct.ledgerBalAmt
            )
            
            // Save all entities
            if (!result.fundingAcct.save() || !result.destinationAcct.save() || 
                !txnFile1.save(flush: true, validate: false) || !txnFile2.save(flush: true, validate: false) ||
                !acctLedger1.save(flush: true, validate: false) || !acctLedger2.save(flush: true, validate: false)) {
                return [error: "Fund transfer save failed"]
            }
            
            // Create fund transfer record
            def txnFt = new TxnDepositFundTransfer(
                branch: UserMaster.get(userId).branch,
                drDeposit: result.fundingAcct, 
                crDeposit: result.destinationAcct,
                drTxn: txnFile1, 
                crTxn: txnFile2, 
                fundTransferAmt: amount, 
                txnParticulars: params.txnDescription,
                txnRef: params.txnRef, 
                currency: result.fundingAcct.product.currency.id,
                txnDate: result.fundingAcct.branch.runDate, 
                user: UserMaster.get(userId), 
                txnTemplate: params.txnTemplate
            )
            txnFt.save(flush: true)
            
            // Process GL entries
            glTransactionService.saveDepositFundTransferGl(txnFt.id)
            
            // Audit logging
            auditLogService.insert('080', 'DEP03003', 
                "Fund transfer - Amount: ${amount}, From: ${result.fundingAcct.acctNo}, To: ${result.destinationAcct.acctNo}", 
                'DepositTransactionService', null, null, null, result.fundingAcct.id)
            
            log.info("Fund transfer processed successfully")
            return result
            
        } catch (Exception e) {
            log.error("Error processing fund transfer", e)
            return [error: "Fund transfer failed: ${e.message}"]
        }
    }
}
