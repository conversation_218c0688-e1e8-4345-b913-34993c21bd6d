package org.icbs.deposit

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j

import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Institution
import org.icbs.admin.TxnTemplate
import org.icbs.deposit.Deposit
import org.icbs.deposit.DepositBranchTransfer
import org.icbs.deposit.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.ConfigItemStatus

/**
 * REFACTORED: DepositAccountManagementService
 * Extracted from DepositService.groovy (2,035 lines)
 * Handles deposit account management operations with modern patterns
 * 
 * This service manages:
 * - Account number generation
 * - Account initialization and setup
 * - Branch transfers
 * - Account status updates
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III (Service Decomposition)
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class DepositAccountManagementService {
    
    // Service Dependencies
    def glTransactionService
    def auditLogService
    
    /**
     * Generate account number for deposit
     */
    def generateAccountNo(Deposit depositInstance) {
        log.info("Generating account number for deposit")
        
        try {
            int[] piArray = [1, 4, 1, 5, 9, 2, 6, 5, 3, 5, 8]
            String accountNo
            String branchCode
            String productCode
            
            def result = Deposit.executeQuery(
                "select max(SUBSTRING(acctNo, 9,5)) from Deposit where SUBSTRING(acctNo, 1,7) = CONCAT(:branch,'-',:prod))", 
                [branch: String.format("%03d", depositInstance?.branch?.code), 
                 prod: String.format("%03d", depositInstance?.product?.code)]
            )
            
            int serialNum
            String serialOld 
            if (result[0] != null) {
                serialOld = result[0]
                serialNum = Integer.parseInt(serialOld)
            } else {
                serialNum = 0
            } 

            serialNum++
            
            branchCode = String.format("%03d", depositInstance?.branch?.code)
            productCode = String.format("%03d", depositInstance?.product?.code)
            String serialCode = String.format("%05d", serialNum)

            accountNo = branchCode + productCode + serialCode
            
            def checksum = 0
            for(int i = accountNo.length() - 1; i >= 0; i--) {
                checksum += Character.getNumericValue(accountNo.charAt(i)) * piArray[i]
            }
            
            String checkBit = (checksum % 10).toString()
            
            depositInstance.acctNo = branchCode + "-" + productCode + "-" + serialCode + "-" + checkBit
            depositInstance.openingDate = Branch?.get(1).runDate
            
            log.info("Generated account number: ${depositInstance.acctNo}")
            depositInstance.save(failOnError: true)
            
            return depositInstance.acctNo
            
        } catch (Exception e) {
            log.error("Error generating account number", e)
            throw new RuntimeException("Failed to generate account number", e)
        }
    }
    
    /**
     * Initialize deposit account with default values
     */
    def initializeDeposit(Deposit depositInstance) {
        log.info("Initializing deposit account: ${depositInstance?.acctNo}")
        
        try {
            // Set default balances
            depositInstance.ledgerBalAmt = depositInstance.ledgerBalAmt ?: 0.0
            depositInstance.availableBalAmt = depositInstance.availableBalAmt ?: 0.0
            depositInstance.interestBalAmt = depositInstance.interestBalAmt ?: 0.0
            depositInstance.holdBalAmt = depositInstance.holdBalAmt ?: 0.0
            
            // Set default dates
            if (!depositInstance.openingDate) {
                depositInstance.openingDate = Branch?.get(1).runDate
            }
            
            // Set transaction sequence
            depositInstance.transactionSequenceNo = depositInstance.transactionSequenceNo ?: 0
            
            // Set default status if not set
            if (!depositInstance.status) {
                depositInstance.status = org.icbs.lov.DepositStatus.get(1) // Pending
            }
            
            depositInstance.save(flush: true, failOnError: true)
            
            log.info("Deposit account initialized successfully")
            return depositInstance
            
        } catch (Exception e) {
            log.error("Error initializing deposit account", e)
            throw new RuntimeException("Failed to initialize deposit account", e)
        }
    }
    
    /**
     * Update deposit branch
     */
    def updateBranch(Deposit depositInstance, Branch branch, String particulars, String reference, UserMaster user) {
        log.info("Updating branch for deposit: ${depositInstance.acctNo} from ${depositInstance.branch.name} to ${branch.name}")
        
        try {
            def oldBranch = depositInstance.branch
            depositInstance.branch = branch
            depositInstance.save(flush: true, failOnError: true)
            
            def tmpDr = TxnTemplate.get(Institution.findByParamCode("DEP.40121").paramValue.toInteger())
            def tmpCr = TxnTemplate.get(Institution.findByParamCode("DEP.40122").paramValue.toInteger())
            
            def trDr = new TxnFile(
                acctNo: depositInstance.acctNo, 
                branch: oldBranch, 
                currency: depositInstance.product.currency,
                depAcct: depositInstance, 
                status: ConfigItemStatus.read(2), 
                txnAmt: depositInstance.ledgerBalAmt, 
                txnCode: tmpDr.code,
                txnDate: branch.runDate, 
                txnDescription: 'Deposit Branch Transfer Debit', 
                txnParticulars: particulars,
                txnRef: reference, 
                txnTemplate: tmpDr, 
                txnType: tmpDr.txnType,
                txnTimestamp: new Date().toTimestamp(), 
                user: user
            )
            trDr.save(flush: true) 
            
            def trCr = new TxnFile(
                acctNo: depositInstance.acctNo, 
                branch: depositInstance.branch, 
                currency: depositInstance.product.currency,
                depAcct: depositInstance, 
                status: ConfigItemStatus.read(2), 
                txnAmt: depositInstance.ledgerBalAmt, 
                txnCode: tmpCr.code,
                txnDate: branch.runDate, 
                txnDescription: 'Deposit Branch Transfer Credit', 
                txnParticulars: particulars,
                txnRef: reference, 
                txnTemplate: tmpCr, 
                txnType: tmpCr.txnType,
                txnTimestamp: new Date().toTimestamp(), 
                user: user
            )
            trCr.save(flush: true)    
            
            def acctLedger1 = new TxnDepositAcctLedger(
                txnType: tmpDr.txnType,
                user: user,
                branch: oldBranch,
                currency: depositInstance.product.currency, 
                status: depositInstance.status,
                txnDate: branch.runDate,
                acct: depositInstance,
                acctNo: depositInstance.acctNo, 
                creditAmt: depositInstance.ledgerBalAmt,
                bal: depositInstance.ledgerBalAmt,
                txnRef: reference,
                debitAmt: depositInstance.ledgerBalAmt,
                txnFile: trDr, 
                passbookBal: 0.00D,
                txnCode: tmpDr.code
            )
            
            acctLedger1.save(flush: true, failOnError: true)

            def d = new DepositBranchTransfer(
                deposit: depositInstance, 
                newBranch: branch, 
                oldBranch: oldBranch, 
                depositDr: trDr, 
                depositCr: trCr, 
                transferDate: branch.runDate, 
                particulars: particulars, 
                reference: reference, 
                user: user, 
                userBranch: user.branch
            )
            d.save(flush: true)

            glTransactionService.saveDepositTransferBranchEntry(d)
            
            // Audit logging
            auditLogService.insert('080', 'DEP02001', 
                "Branch transfer - From: ${oldBranch.name}, To: ${branch.name}", 
                'DepositAccountManagementService', null, null, null, depositInstance.id)
            
            log.info("Branch transfer completed successfully")
            return d
            
        } catch (Exception e) {
            log.error("Error updating deposit branch", e)
            throw new RuntimeException("Failed to update deposit branch", e)
        }
    }
    
    /**
     * Update deposit status
     */
    def updateStatus(Deposit depositInstance, def newStatus) {
        log.info("Updating status for deposit: ${depositInstance.acctNo} to ${newStatus}")
        
        try {
            def oldStatus = depositInstance.status
            depositInstance.status = newStatus
            
            // Set status change date for certain statuses
            if (newStatus.id in [6, 7]) { // Closed, Cancelled
                depositInstance.statusChangedDate = depositInstance.branch.runDate
            }
            
            depositInstance.save(flush: true, failOnError: true)
            
            // Audit logging
            auditLogService.insert('080', 'DEP02002', 
                "Status update - From: ${oldStatus?.description}, To: ${newStatus?.description}", 
                'DepositAccountManagementService', null, null, null, depositInstance.id)
            
            log.info("Status updated successfully")
            return depositInstance
            
        } catch (Exception e) {
            log.error("Error updating deposit status", e)
            throw new RuntimeException("Failed to update deposit status", e)
        }
    }
    
    /**
     * Get deposit account summary
     */
    Map getAccountSummary(Long depositId) {
        try {
            Deposit deposit = Deposit.get(depositId)
            if (!deposit) {
                return [error: "Deposit account not found"]
            }
            
            return [
                accountNo: deposit.acctNo,
                customerName: deposit.customer?.fullName,
                productName: deposit.product?.name,
                branchName: deposit.branch?.name,
                status: deposit.status?.description,
                ledgerBalance: deposit.ledgerBalAmt,
                availableBalance: deposit.availableBalAmt,
                holdBalance: deposit.holdBalAmt,
                interestBalance: deposit.interestBalAmt,
                openingDate: deposit.openingDate,
                lastTransactionDate: deposit.lastTransactionDate
            ]
            
        } catch (Exception e) {
            log.error("Error getting account summary", e)
            return [error: "Error retrieving account summary"]
        }
    }
}
